# FindU - AI需求生成器

🚀 基于AI的智能需求生成平台，帮助用户从简单的提示词创建详细的项目需求文档。

## ✨ 功能特性

- **🤖 AI驱动的案例生成**: 从简单的用户提示词生成多个项目案例
- **🎯 交互式案例选择**: 浏览和选择生成的案例
- **📄 自动文档创建**: 生成详细的需求文档
- **🌍 多语言支持**: 支持中文和英文界面
- **💎 现代化UI**: 使用Tailwind CSS的清洁、响应式设计
- **🔧 模块化架构**: 高度可复用和可扩展的组件设计

## 🛠 技术栈

### 前端
- **Next.js 15** (App Router) - React全栈框架
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的JavaScript
- **Tailwind CSS** - 实用优先的CSS框架
- **国际化 (i18n)** - 多语言支持

### 后端
- **FastAPI** - 现代Python Web框架
- **Python 3.8+** - 后端开发语言
- **MongoDB** - NoSQL数据库
- **AI集成** (Qwen3-turbo) - 智能内容生成
- **PDF生成** - 文档导出功能

## 🚀 快速开始

### 系统要求
- Node.js 18+
- Python 3.8+
- MongoDB (可选，用于数据持久化)

### 🔧 安装步骤

#### 方法一：使用启动脚本（推荐）

```bash
# 克隆仓库
git clone <repository-url>
cd FindU

# 运行开发环境启动脚本
./start-dev.sh
```

#### 方法二：手动安装

1. **克隆仓库**:
```bash
git clone <repository-url>
cd FindU
```

2. **安装前端依赖**:
```bash
cd findu-frontend
npm install
```

3. **安装后端依赖**:
```bash
cd ../findu-backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

4. **设置环境变量**:
```bash
# 前端环境变量
cp findu-frontend/.env.example findu-frontend/.env.local

# 后端环境变量
cp findu-backend/.env.example findu-backend/.env
```

5. **配置AI API密钥** (在 `findu-backend/.env` 中):
```env
X=your_actual_api_key_here
```

### 🏃‍♂️ 运行应用

#### 开发模式

1. **启动后端**:
```bash
cd findu-backend
source venv/bin/activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

2. **启动前端**:
```bash
cd findu-frontend
npm run dev
```

3. **访问应用**: http://localhost:3000

#### 使用Docker

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d
```

## 📁 项目结构

```
FindU/
├── findu-frontend/              # Next.js 前端应用
│   ├── app/                     # App Router 页面
│   ├── src/                     # 源代码
│   │   ├── components/          # React 组件
│   │   │   ├── ui/             # UI 组件库
│   │   │   └── ...
│   │   ├── lib/                # 工具库和API客户端
│   │   ├── locales/            # 国际化文件
│   │   └── styles/             # 样式文件
│   └── public/                 # 静态资源
├── findu-backend/              # FastAPI 后端应用
│   ├── app/                    # 应用代码
│   │   ├── routers/            # API 路由
│   │   ├── db/                 # 数据库模型
│   │   └── utils/              # 工具函数
│   └── static/                 # 生成文件存储
├── docker-compose.yml          # Docker部署配置
├── start-dev.sh               # 开发环境启动脚本
└── README.md                  # 项目文档
```

## 🔗 API文档

后端运行后，访问 http://localhost:8000/docs 查看交互式API文档。

### 主要API端点

- `POST /api/generate-cases` - 生成项目案例
- `POST /api/generate-demand` - 生成需求文档
- `GET /health` - 健康检查

## 🎨 UI组件库

项目包含完整的UI组件库，位于 `findu-frontend/src/components/ui/`：

- **Button** - 多种样式和尺寸的按钮组件
- **Input** - 输入框组件
- **Textarea** - 文本域组件（支持自动调整大小）
- **Card** - 卡片组件系统
- **Loading** - 加载动画组件

## 🌍 国际化

支持中文（zh）和英文（en）两种语言：

- 路由: `/zh/` 和 `/en/`
- 翻译文件: `src/locales/zh.json` 和 `src/locales/en.json`
- 自动语言检测和重定向

## 🚀 部署

### 生产环境部署

1. **使用Docker Compose**:
```bash
# 设置环境变量
export X=your_api_key
export JWT_SECRET=your_jwt_secret

# 启动生产环境
docker-compose -f docker-compose.yml up -d
```

2. **手动部署**:
```bash
# 构建前端
cd findu-frontend
npm run build

# 启动后端
cd ../findu-backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 🤝 贡献指南

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 故障排除

### 常见问题

1. **端口冲突**: 确保3000和8000端口未被占用
2. **API密钥**: 确保在`.env`文件中正确配置了`X`
3. **依赖问题**: 删除`node_modules`和重新安装依赖
4. **Python环境**: 确保使用正确的Python版本和虚拟环境

### 获取帮助

- 查看项目Issues页面
- 阅读API文档 (http://localhost:8000/docs)
- 检查日志输出获取详细错误信息
