# FindU 应用部署指南

本文档说明如何配置 FindU 应用的 CI/CD 流程，实现自动测试和部署到服务器。

## 部署架构

FindU 应用将部署到服务器的 `~/official-site/apps/findu/` 目录下，与主站共存：

```
~/official-site/
├── html/                    # 主站静态文件
├── docker-compose.yml       # 主站 nginx 服务
└── apps/
    └── findu/              # FindU 应用
        ├── html/           # FindU 前端静态文件
        ├── findu-backend/  # FindU 后端源码
        ├── docker-compose.yml  # FindU 服务配置
        └── nginx-findu.conf    # Nginx 配置片段
```

## 服务端口分配

- **主站 Nginx**: 端口 8000 (已占用)
- **FindU 前端**: 端口 8001
- **FindU 后端**: 端口 8002

## GitHub Secrets 配置

在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加以下 secrets：

```
SERVER_HOST=你的服务器IP地址
SERVER_USER=ubuntu
SSH_PRIVATE_KEY=你的SSH私钥内容
```

**注意**: 这些 secrets 名称与原始配置文件中的名称保持一致，确保与现有服务器配置兼容。

## CI/CD 工作流程

### 1. Pull Request 触发
- 运行前端测试 (Vitest)
- 运行后端测试 (pytest)
- **不进行部署**

### 2. Push to main 触发
- 运行前端测试 (Vitest)
- 运行后端测试 (pytest)
- 构建前端静态文件
- 部署到服务器

## 部署步骤

1. **构建前端**: 使用 `npm run build` 和 `npm run export` 生成静态文件
2. **上传文件**: 
   - docker-compose.yml → `~/official-site/apps/findu/`
   - 前端静态文件 → `~/official-site/apps/findu/html/`
   - 后端源码 → `~/official-site/apps/findu/findu-backend/`
   - Nginx 配置 → `~/official-site/apps/findu/nginx-findu.conf`
3. **启动服务**: 使用 Docker Compose 启动容器

## 部署后配置

### 1. 配置环境变量
```bash
cd ~/official-site/apps/findu
nano .env
```

### 2. 配置 Nginx
使用提供的设置脚本：
```bash
cd ~/official-site/apps/findu
./setup-nginx.sh
```

或手动配置：
```nginx
# 在主站的 nginx 配置文件中添加
include /home/<USER>/official-site/apps/findu/nginx-findu.conf;
```

### 3. 重启服务
```bash
cd ~/official-site/apps/findu
docker compose restart
sudo systemctl reload nginx
```

## 访问地址

部署完成后，FindU 应用可通过以下地址访问：
- 前端: `http://你的域名/findu/`
- API: `http://你的域名/findu/api/`
- 静态文件: `http://你的域名/findu/static/`

## 环境变量配置

### 自动配置
部署脚本会自动创建 `.env` 文件（如果不存在）。首次部署后，请编辑配置：

```bash
cd ~/official-site/apps/findu
cp .env.example .env
nano .env
```

### 必需配置项
```bash
# JWT 密钥（必须修改）
JWT_SECRET=your_production_jwt_secret_here

# AI 服务配置（必须配置）
AI_API_KEY=your_ai_api_key_here
AI_API_URL=https://api.openai.com/v1/chat/completions
AI_MODEL=gpt-3.5-turbo
AI_PROVIDER=openai

# 存储配置（可选，使用默认值）
STORAGE_URL=https://your-domain.com/findu/static
FRONTEND_URL=https://your-domain.com
```

### AI 服务提供商选择
支持多种 AI 服务提供商：

**OpenAI**:
```bash
AI_API_KEY=sk-your-openai-key
AI_API_URL=https://api.openai.com/v1/chat/completions
AI_MODEL=gpt-3.5-turbo
AI_PROVIDER=openai
```

**通义千问**:
```bash
AI_API_KEY=your-dashscope-key
AI_API_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
AI_MODEL=qwen-turbo
AI_PROVIDER=qwen
```

**其他兼容服务**:
```bash
AI_API_KEY=your-api-key
AI_API_URL=https://your-service.com/v1/chat/completions
AI_MODEL=your-model
AI_PROVIDER=custom
```

## 故障排除

### 查看服务状态
```bash
cd ~/official-site/apps/findu
docker compose ps
docker compose logs
```

### 重启服务
```bash
cd ~/official-site/apps/findu
docker compose restart
```

### 查看端口占用
```bash
netstat -tlnp | grep -E ':(8001|8002)'
```
