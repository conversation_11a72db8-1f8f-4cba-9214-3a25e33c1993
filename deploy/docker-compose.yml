version: '3.9'
services:
  # FindU 前端静态文件服务
  findu-frontend:
    image: nginx:alpine
    container_name: findu-frontend-static
    restart: unless-stopped
    ports:
      - "8001:80"  # 前端服务端口，避免与主站nginx冲突
    volumes:
      - ./html:/usr/share/nginx/html:ro
    networks:
      - findu-network

  # FindU 后端API服务
  findu-backend:
    build:
      context: ./findu-backend
      dockerfile: Dockerfile
    container_name: findu-backend-api
    restart: unless-stopped
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - JWT_SECRET=${JWT_SECRET:-findu_default_secret}
      - STORAGE_PATH=static/demands
      - STORAGE_URL=${STORAGE_URL:-http://127.0.0.1:8002}
      # 允许来自主域名和本地的请求
      - FRONTEND_URL=${FRONTEND_URL:-*}
      # AI服务配置
      - AI_API_KEY=${AI_API_KEY:-}
      - AI_API_URL=${AI_API_URL:-}
      - AI_MODEL=${AI_MODEL:-}
      - AI_PROVIDER=${AI_PROVIDER:-custom}
    ports:
      - "8002:8000"  # 后端API端口
    volumes:
      - backend_storage:/app/static
    networks:
      - findu-network
    depends_on:
      - findu-frontend

volumes:
  backend_storage:

networks:
  findu-network:
    driver: bridge

