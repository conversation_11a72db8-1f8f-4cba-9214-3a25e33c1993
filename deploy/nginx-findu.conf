# FindU 应用的 Nginx 配置
# 此配置文件应该被包含在主 Nginx 配置中，或者作为 sites-available 配置

# FindU 前端静态文件代理
location /findu/ {
    # 代理到 FindU 前端容器
    proxy_pass http://127.0.0.1:8001/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 处理静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:8001;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# FindU API 代理
location /findu/api/ {
    # 代理到 FindU 后端容器
    proxy_pass http://127.0.0.1:8002/api/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # API 特殊配置
    proxy_read_timeout 300;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;
}

# FindU 静态文件存储代理（用于文档下载等）
location /findu/static/ {
    proxy_pass http://127.0.0.1:8002/static/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
