#!/bin/bash

# FindU Nginx 配置设置脚本
# 此脚本帮助将 FindU 应用集成到现有的 Nginx 配置中

set -e

echo "🔧 FindU Nginx 配置设置"
echo "========================"

# 检查是否在正确的目录
if [ ! -f "nginx-findu.conf" ]; then
    echo "❌ 错误: 请在 ~/official-site/apps/findu/ 目录下运行此脚本"
    exit 1
fi

# 检查主站 Nginx 配置目录
NGINX_CONF_DIR="/etc/nginx"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"

if [ ! -d "$NGINX_CONF_DIR" ]; then
    echo "❌ 错误: 未找到 Nginx 配置目录 $NGINX_CONF_DIR"
    echo "请确保 Nginx 已正确安装"
    exit 1
fi

echo "📁 找到 Nginx 配置目录: $NGINX_CONF_DIR"

# 检查当前用户权限
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  注意: 需要 sudo 权限来修改 Nginx 配置"
    SUDO="sudo"
else
    SUDO=""
fi

# 显示配置内容
echo ""
echo "📄 FindU Nginx 配置内容:"
echo "------------------------"
cat nginx-findu.conf
echo "------------------------"

echo ""
echo "🔧 配置选项:"
echo "1. 将配置添加到现有站点配置文件"
echo "2. 创建独立的配置文件"
echo "3. 仅显示配置内容（手动配置）"
echo "4. 退出"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "📝 将配置添加到现有站点配置..."
        
        # 查找现有的站点配置文件
        if [ -d "$SITES_AVAILABLE" ]; then
            echo "可用的站点配置文件:"
            ls -la "$SITES_AVAILABLE"
            echo ""
            read -p "请输入要修改的配置文件名 (例如: default): " site_name
            
            SITE_FILE="$SITES_AVAILABLE/$site_name"
            if [ -f "$SITE_FILE" ]; then
                echo "📋 备份原配置文件..."
                $SUDO cp "$SITE_FILE" "$SITE_FILE.backup.$(date +%Y%m%d_%H%M%S)"
                
                echo "📝 添加 FindU 配置到 $SITE_FILE..."
                echo ""
                echo "请手动将以下内容添加到 server 块中:"
                echo "------------------------"
                cat nginx-findu.conf
                echo "------------------------"
                echo ""
                echo "💡 提示: 使用 sudo nano $SITE_FILE 编辑配置文件"
            else
                echo "❌ 错误: 配置文件 $SITE_FILE 不存在"
            fi
        else
            echo "❌ 错误: 未找到 sites-available 目录"
        fi
        ;;
    2)
        echo ""
        echo "📝 创建独立的 FindU 配置文件..."
        
        FINDU_CONF="$SITES_AVAILABLE/findu"
        
        echo "创建配置文件: $FINDU_CONF"
        $SUDO tee "$FINDU_CONF" > /dev/null << EOF
# FindU 应用配置
# 此配置应该被包含在主站点配置中

$(cat nginx-findu.conf)
EOF
        
        echo "✅ 配置文件已创建: $FINDU_CONF"
        echo ""
        echo "💡 下一步: 在主站点配置中包含此文件:"
        echo "   include $FINDU_CONF;"
        ;;
    3)
        echo ""
        echo "📄 FindU Nginx 配置内容:"
        echo "------------------------"
        cat nginx-findu.conf
        echo "------------------------"
        echo ""
        echo "💡 请手动将上述配置添加到您的 Nginx 站点配置中"
        ;;
    4)
        echo "👋 退出配置"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔄 测试 Nginx 配置..."
if $SUDO nginx -t; then
    echo "✅ Nginx 配置测试通过"
    echo ""
    read -p "是否重新加载 Nginx 配置? (y/N): " reload
    if [[ $reload =~ ^[Yy]$ ]]; then
        echo "🔄 重新加载 Nginx..."
        $SUDO systemctl reload nginx
        echo "✅ Nginx 配置已重新加载"
    fi
else
    echo "❌ Nginx 配置测试失败，请检查配置"
fi

echo ""
echo "🎉 配置完成！"
echo ""
echo "📍 访问地址:"
echo "   前端: http://your-domain/findu/"
echo "   API:  http://your-domain/findu/api/"
echo ""
echo "💡 提示:"
echo "   - 确保 FindU 服务正在运行: docker compose ps"
echo "   - 检查服务日志: docker compose logs"
echo "   - 配置 AI 服务: 编辑 .env 文件"
