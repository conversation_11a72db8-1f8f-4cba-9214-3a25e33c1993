services:
  # 后端服务
  backend:
    build:
      context: ./findu-backend
      dockerfile: Dockerfile
    container_name: findu-backend
    restart: always
    env_file: .env # 加载环境变量文件
    # 移除端口映射，由 Nginx 反向代理
    # ports:
    #   - "8000:8000"
    volumes:
      - backend_storage:/app/static
      - sqlite_data:/app/data  # SQLite 数据库文件持久化
    networks:
      - findu-network
      - shared_network
    # 定义健康检查，确保服务可用
    # In your backend service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/docs"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s # Increase the grace period to 60 seconds

  # 前端服务
  frontend:
    build:
      context: ./findu-frontend
      dockerfile: Dockerfile
      args: # <--- 新增 build.args
        - NEXT_PUBLIC_API_URL=/api
    container_name: findu-frontend
    restart: always
    environment:
      # 在容器内部，使用后端服务的名称作为域名
      - NEXT_PUBLIC_API_URL=/api
      - NEXT_PUBLIC_APP_NAME=AI需求生成器
      - NEXT_PUBLIC_DEFAULT_LOCALE=en
    # 移除端口映射，由 Nginx 反向代理
    # ports:
    #   - "3000:3000"
    networks:
      - findu-network # 可以保留原网络
      - shared_network # 新增共享网络
    depends_on:
      backend:
        condition: service_healthy
  
  # # Nginx 反向代理（已在服务器中独立部署）
  # nginx:
  #   image: nginx:1.25.3-alpine
  #   container_name: findu-nginx
  #   restart: always
  #   ports:
  #     - "80:80" # 映射到主机的 80 端口
  #     - "443:443" # 映射到主机的 443 端口（用于 HTTPS）
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro # 映射 Nginx 配置文件
  #     # 如果需要，这里可以映射 SSL 证书
  #   networks:
  #     - findu-network
  #   depends_on:
  #     - frontend
  #     - backend

volumes:
  backend_storage:
  sqlite_data:

networks:
  findu-network:
    driver: bridge
  shared_network: # 新增网络声明
    external: true # 标记为外部网络