#!/bin/bash

# FindU Docker 环境检查和设置脚本
# 该脚本检查Docker环境并提供设置指导

echo "=== FindU Docker 环境检查 ==="
echo

# 检查Docker是否安装
echo "1. 检查Docker安装状态..."
if command -v docker &> /dev/null; then
    echo "✅ Docker 已安装"
    docker --version
else
    echo "❌ Docker 未安装"
    echo "请运行以下命令安装Docker:"
    echo "  sudo apt update"
    echo "  sudo apt install docker.io"
    echo "  sudo systemctl start docker"
    echo "  sudo systemctl enable docker"
    echo "  sudo usermod -aG docker \$USER"
    echo "  # 注销并重新登录以使组权限生效"
    echo
fi

# 检查Docker Compose是否可用
echo "2. 检查Docker Compose状态..."
if command -v docker &> /dev/null; then
    if docker compose version &> /dev/null; then
        echo "✅ Docker Compose 已安装 (新版本)"
        docker compose version
    elif command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose 已安装 (旧版本)"
        docker-compose --version
    else
        echo "❌ Docker Compose 未安装"
        echo "请运行以下命令安装Docker Compose:"
        echo "  sudo apt install docker-compose-plugin"
        echo
    fi
else
    echo "⏭️  跳过 Docker Compose 检查 (Docker 未安装)"
fi

# 检查Docker服务状态
echo "3. 检查Docker服务状态..."
if command -v docker &> /dev/null; then
    if systemctl is-active --quiet docker; then
        echo "✅ Docker 服务正在运行"
    else
        echo "❌ Docker 服务未运行"
        echo "请运行以下命令启动Docker服务:"
        echo "  sudo systemctl start docker"
        echo
    fi
else
    echo "⏭️  跳过 Docker 服务检查 (Docker 未安装)"
fi

# 检查用户权限
echo "4. 检查Docker用户权限..."
if command -v docker &> /dev/null; then
    if docker ps &> /dev/null; then
        echo "✅ 用户具有Docker权限"
    else
        echo "❌ 用户没有Docker权限"
        echo "请运行以下命令添加用户到docker组:"
        echo "  sudo usermod -aG docker \$USER"
        echo "  # 注销并重新登录以使组权限生效"
        echo
    fi
else
    echo "⏭️  跳过 Docker 权限检查 (Docker 未安装)"
fi

# 检查必要文件
echo "5. 检查项目文件..."
files_to_check=("docker-compose.yml" ".env" "nginx/nginx.conf" "findu-frontend/Dockerfile" "findu-backend/Dockerfile")
all_files_exist=true

for file in "${files_to_check[@]}"; do
    if [[ -f "$file" ]]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        all_files_exist=false
    fi
done

if $all_files_exist; then
    echo "✅ 所有必要文件都存在"
else
    echo "❌ 缺少必要文件，请检查项目结构"
fi

echo
echo "=== 检查完成 ==="

# 如果所有检查都通过，提供运行指导
if command -v docker &> /dev/null && \
   (docker compose version &> /dev/null || command -v docker-compose &> /dev/null) && \
   systemctl is-active --quiet docker && \
   docker ps &> /dev/null && \
   $all_files_exist; then
    echo
    echo "🎉 环境检查通过！可以运行以下命令启动项目:"
    echo
    if docker compose version &> /dev/null; then
        echo "  # 构建并启动所有服务"
        echo "  docker compose up --build"
        echo
        echo "  # 后台运行"
        echo "  docker compose up --build -d"
        echo
        echo "  # 停止服务"
        echo "  docker compose down"
        echo
        echo "  # 完全清理（包括数据卷）"
        echo "  docker compose down --volumes --remove-orphans"
    else
        echo "  # 构建并启动所有服务"
        echo "  docker-compose up --build"
        echo
        echo "  # 后台运行"
        echo "  docker-compose up --build -d"
        echo
        echo "  # 停止服务"
        echo "  docker-compose down"
        echo
        echo "  # 完全清理（包括数据卷）"
        echo "  docker-compose down --volumes --remove-orphans"
    fi
    echo
    echo "项目启动后可以通过以下地址访问:"
    echo "  - 前端应用: http://localhost"
    echo "  - 后端API文档: http://localhost/docs"
else
    echo
    echo "❌ 环境检查未通过，请根据上述提示解决问题后再试"
fi
