# FindU 测试文档

本文档详细介绍了 FindU 项目的完整测试策略、测试集和 CI/CD 流程。

## 📋 测试概览

### 测试覆盖范围
- **前端测试**: 单元测试、集成测试、E2E测试
- **后端测试**: 单元测试、集成测试、API测试、性能测试
- **CI/CD**: 自动化测试、代码质量检查、安全扫描、部署流程

### 测试技术栈
- **前端**: Jest, React Testing Library, Playwright
- **后端**: pytest, pytest-asyncio, httpx
- **CI/CD**: GitHub Actions, Docker, CodeQL, Snyk

## 🎯 前端测试

### 单元测试
位置: `findu-frontend/src/components/**/__tests__/`

#### UI组件测试
- **Button组件**: 测试不同变体、尺寸、状态
- **Card组件**: 测试布局、交互、组合使用
- **其他UI组件**: Input, Textarea, Loading等

#### 业务组件测试
- **HomeClient**: 主要业务逻辑测试
- **CaseCard**: 案例展示和选择
- **DocumentPreview**: 文档预览功能

#### 工具函数测试
- **API客户端**: HTTP请求、错误处理
- **国际化**: 翻译功能、语言切换
- **工具函数**: 通用工具方法

### 集成测试
- API集成测试
- 组件间交互测试
- 状态管理测试

### E2E测试
位置: `findu-frontend/e2e/`

#### 测试场景
- 完整用户流程测试
- 跨浏览器兼容性
- 响应式设计验证
- 错误处理测试

### 运行前端测试

```bash
cd findu-frontend

# 安装依赖
npm ci

# 运行单元测试
npm run test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 运行E2E测试
npm run e2e

# 运行E2E测试（可视化模式）
npm run e2e:ui
```

## 🔧 后端测试

### 单元测试
位置: `findu-backend/tests/`

#### API路由测试
- **Cases API**: 案例生成接口测试
- **Documents API**: 文档生成接口测试
- **Static Files API**: 静态文件管理

#### 服务层测试
- **AI服务**: AI接口调用、错误处理、超时处理
- **文档服务**: PDF/Word/TXT生成、文件保存
- **数据库服务**: MongoDB操作、数据持久化

#### 工具函数测试
- 认证和授权
- 文件处理
- 配置管理

### 集成测试
- API端点集成测试
- 数据库集成测试
- 外部服务集成测试
- 完整工作流测试

### 性能测试
- API响应时间测试
- 并发处理测试
- 内存使用测试
- 吞吐量测试

### 运行后端测试

```bash
cd findu-backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 运行所有测试
pytest

# 运行特定类型的测试
pytest -m unit          # 单元测试
pytest -m integration   # 集成测试
pytest -m performance   # 性能测试

# 生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行代码质量检查
black --check .
isort --check-only .
mypy app --ignore-missing-imports
```

## 🚀 CI/CD 流程

### GitHub Actions 工作流
位置: `.github/workflows/ci.yml`

#### CI 阶段
1. **代码检查**
   - ESLint (前端)
   - Black, isort, mypy (后端)
   - TypeScript 类型检查

2. **测试执行**
   - 前端单元测试
   - 后端单元测试和集成测试
   - E2E测试

3. **安全扫描**
   - CodeQL 代码分析
   - Snyk 依赖安全扫描

4. **覆盖率报告**
   - 前端测试覆盖率
   - 后端测试覆盖率
   - 上传到 Codecov

#### CD 阶段
1. **Docker 镜像构建**
   - 前端镜像构建和推送
   - 后端镜像构建和推送

2. **部署准备**
   - 环境配置验证
   - 健康检查准备

3. **部署执行** (预留)
   - 生产环境部署
   - 健康检查验证

### 本地测试运行

#### 使用测试脚本
```bash
# 运行所有测试
./scripts/run-tests.sh

# 只运行前端测试
./scripts/run-tests.sh --frontend-only

# 只运行后端测试
./scripts/run-tests.sh --backend-only

# 包含E2E测试
./scripts/run-tests.sh --with-e2e

# 包含性能测试
./scripts/run-tests.sh --with-performance

# 包含安全检查
./scripts/run-tests.sh --with-security
```

#### 使用Docker测试
```bash
# 运行完整测试套件
docker-compose -f docker-compose.test.yml up --build

# 只运行特定服务测试
docker-compose -f docker-compose.test.yml up backend-test
docker-compose -f docker-compose.test.yml up frontend-test
docker-compose -f docker-compose.test.yml up e2e-test

# 清理测试环境
docker-compose -f docker-compose.test.yml down -v
```

## 📊 测试覆盖率目标

### 覆盖率要求
- **前端**: 最低 70% 代码覆盖率
- **后端**: 最低 70% 代码覆盖率
- **关键路径**: 90% 以上覆盖率

### 覆盖率报告
- **前端**: `findu-frontend/coverage/lcov-report/index.html`
- **后端**: `findu-backend/htmlcov/index.html`

## 🔍 代码质量标准

### 前端代码质量
- **ESLint**: 代码风格和最佳实践
- **TypeScript**: 类型安全检查
- **Prettier**: 代码格式化

### 后端代码质量
- **Black**: 代码格式化
- **isort**: 导入排序
- **mypy**: 类型检查
- **bandit**: 安全检查

## 🚨 测试最佳实践

### 测试编写原则
1. **单一职责**: 每个测试只验证一个功能点
2. **独立性**: 测试之间不应相互依赖
3. **可重复性**: 测试结果应该一致
4. **快速执行**: 单元测试应该快速完成
5. **清晰命名**: 测试名称应该清楚表达测试内容

### Mock 和 Stub 使用
- 外部API调用必须Mock
- 数据库操作在单元测试中Mock
- 文件系统操作使用临时目录
- 时间相关测试使用固定时间

### 测试数据管理
- 使用工厂函数生成测试数据
- 避免硬编码测试数据
- 清理测试产生的副作用

## 🛠️ 故障排除

### 常见问题
1. **测试环境配置**: 确保环境变量正确设置
2. **依赖版本冲突**: 使用锁定的依赖版本
3. **异步测试**: 正确处理异步操作
4. **Mock配置**: 确保Mock配置正确

### 调试技巧
- 使用 `--verbose` 选项查看详细输出
- 使用 `--pdb` 选项进入调试器
- 检查测试覆盖率报告找出未测试代码
- 使用日志输出调试测试问题

## 📈 持续改进

### 测试指标监控
- 测试执行时间趋势
- 测试覆盖率变化
- 测试失败率统计
- 代码质量指标

### 定期维护
- 更新测试依赖
- 重构过时的测试
- 添加新功能的测试
- 优化测试执行性能

---

通过这套完整的测试体系，FindU 项目能够确保代码质量、功能正确性和系统稳定性，为未来的功能扩展和维护提供坚实的基础。
