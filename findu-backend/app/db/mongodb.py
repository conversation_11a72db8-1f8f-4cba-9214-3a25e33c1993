"""
MongoDB 数据库操作模块

该模块提供 MongoDB 数据库的连接和操作接口。
主要功能包括：
- 数据库连接管理
- 用户提示词存储
- 生成案例数据持久化
- 用户行为数据记录

数据库结构：
- prompts 集合：存储用户输入的提示词
- cases 集合：存储 AI 生成的案例数据
- users 集合：用户信息管理（预留）
- documents 集合：生成的文档记录（预留）

注意：当前为 MVP 版本，数据库功能已实现但暂未集成到主业务流程中。
所有接口都已预留，可在后续版本中轻松启用数据持久化功能。

技术实现：
- 使用 pymongo 作为 MongoDB 客户端
- 支持连接池和自动重连
- 采用集合分离的设计模式
"""

from pymongo import MongoClient
import os
from datetime import datetime
from typing import Dict, List, Optional
import logging

# 配置日志
logger = logging.getLogger(__name__)

class MongoDB:
    """
    MongoDB 数据库操作类

    提供对 FindU 系统所需的所有数据库操作接口。
    采用单例模式确保数据库连接的复用和一致性。

    Attributes:
        client: MongoDB 客户端实例
        db: 数据库实例，默认使用 "demand_ai" 数据库
    """

    def __init__(self):
        """
        初始化 MongoDB 连接

        从环境变量读取数据库连接字符串，建立与 MongoDB 的连接。
        如果连接失败，会记录错误日志但不会中断应用启动。
        """
        try:
            # 从环境变量获取 MongoDB 连接 URL
            mongo_url = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
            self.client = MongoClient(mongo_url)

            # 选择数据库，如果不存在会自动创建
            self.db = self.client["demand_ai"]

            # 测试连接
            self.client.admin.command('ping')
            logger.info("MongoDB 连接成功")

        except Exception as e:
            logger.error(f"MongoDB 连接失败: {str(e)}")
            # 在 MVP 版本中，数据库连接失败不应中断服务
            self.client = None
            self.db = None

    def save_prompt(self, user_id: str, prompt: str, locale: str) -> Optional[str]:
        """
        保存用户输入的提示词

        将用户的需求提示词存储到数据库中，用于后续的数据分析和用户行为追踪。

        Args:
            user_id (str): 用户唯一标识符
            prompt (str): 用户输入的需求描述
            locale (str): 语言代码

        Returns:
            Optional[str]: 如果保存成功返回文档 ID，失败返回 None

        Example:
            >>> db = MongoDB()
            >>> prompt_id = db.save_prompt("user123", "我需要一个电商网站", "zh")
            >>> print(f"提示词已保存，ID: {prompt_id}")
        """
        if not self.db:
            logger.warning("数据库未连接，跳过提示词保存")
            return None

        try:
            collection = self.db["prompts"]
            document = {
                "user_id": user_id,
                "prompt": prompt,
                "locale": locale,
                "created_at": datetime.utcnow(),
                "status": "processed"  # 处理状态：pending, processed, failed
            }
            result = collection.insert_one(document)
            logger.info(f"提示词已保存，ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"保存提示词失败: {str(e)}")
            return None

    def save_case(self, prompt_id: str, case_data: dict) -> Optional[str]:
        """
        保存 AI 生成的案例数据

        将 AI 服务生成的案例信息存储到数据库中，建立与原始提示词的关联。

        Args:
            prompt_id (str): 关联的提示词 ID
            case_data (dict): 案例数据，包含 title, description, details 等字段

        Returns:
            Optional[str]: 如果保存成功返回文档 ID，失败返回 None

        Example:
            >>> case = {
            ...     "id": 0,
            ...     "title": "电商网站",
            ...     "description": "在线购物平台",
            ...     "details": ["用户注册", "商品展示", "购物车"]
            ... }
            >>> case_id = db.save_case("prompt123", case)
        """
        if not self.db:
            logger.warning("数据库未连接，跳过案例保存")
            return None

        try:
            collection = self.db["cases"]
            document = {
                "prompt_id": prompt_id,
                "created_at": datetime.utcnow(),
                **case_data  # 展开案例数据
            }
            result = collection.insert_one(document)
            logger.info(f"案例已保存，ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"保存案例失败: {str(e)}")
            return None

    def save_document_record(self, case_id: str, document_url: str, locale: str) -> Optional[str]:
        """
        保存文档生成记录

        记录用户生成的需求文档信息，用于统计和管理。

        Args:
            case_id (str): 关联的案例 ID
            document_url (str): 生成的文档 URL
            locale (str): 文档语言

        Returns:
            Optional[str]: 如果保存成功返回文档 ID，失败返回 None

        Note:
            该功能为预留接口，当前 MVP 版本暂未启用
        """
        if not self.db:
            logger.warning("数据库未连接，跳过文档记录保存")
            return None

        try:
            collection = self.db["documents"]
            document = {
                "case_id": case_id,
                "document_url": document_url,
                "locale": locale,
                "created_at": datetime.utcnow(),
                "download_count": 0  # 下载次数统计
            }
            result = collection.insert_one(document)
            logger.info(f"文档记录已保存，ID: {result.inserted_id}")
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"保存文档记录失败: {str(e)}")
            return None

    def get_user_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """
        获取用户历史记录

        查询用户的历史提示词和生成的案例，用于个性化推荐。

        Args:
            user_id (str): 用户 ID
            limit (int): 返回记录数量限制

        Returns:
            List[Dict]: 用户历史记录列表

        Note:
            该功能为预留接口，用于未来的用户个性化功能
        """
        if not self.db:
            logger.warning("数据库未连接，返回空历史记录")
            return []

        try:
            collection = self.db["prompts"]
            cursor = collection.find(
                {"user_id": user_id}
            ).sort("created_at", -1).limit(limit)

            return list(cursor)
        except Exception as e:
            logger.error(f"获取用户历史失败: {str(e)}")
            return []

    def get_popular_cases(self, locale: str = "en", limit: int = 5) -> List[Dict]:
        """
        获取热门案例

        查询最受欢迎的案例类型，用于首页推荐。

        Args:
            locale (str): 语言代码
            limit (int): 返回案例数量

        Returns:
            List[Dict]: 热门案例列表

        Note:
            该功能为预留接口，用于未来的案例推荐功能
        """
        if not self.db:
            logger.warning("数据库未连接，返回空推荐列表")
            return []

        try:
            # 这里可以实现复杂的推荐算法
            # 当前返回最近创建的案例作为示例
            collection = self.db["cases"]
            cursor = collection.find().sort("created_at", -1).limit(limit)

            return list(cursor)
        except Exception as e:
            logger.error(f"获取热门案例失败: {str(e)}")
            return []

# 创建全局数据库实例
# 在 MVP 版本中，该实例已创建但暂未在主业务流程中使用
# 可通过配置开关在后续版本中启用数据持久化功能
mongo_db = MongoDB()