"""
SQLite 数据库操作模块

该模块提供 SQLite 数据库的连接和操作接口，替代原有的 MongoDB 实现。
主要功能包括：
- 数据库连接管理
- 用户提示词存储
- 生成案例数据持久化
- 用户行为数据记录

数据库结构：
- prompts 表：存储用户输入的提示词
- cases 表：存储 AI 生成的案例数据
- users 表：用户信息管理（预留）
- documents 表：生成的文档记录（预留）

技术实现：
- 使用 sqlite3 作为数据库引擎
- 支持连接池和事务管理
- 采用表分离的设计模式
- 提供与 MongoDB 版本兼容的接口
"""

import sqlite3
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path
import threading

# 配置日志
logger = logging.getLogger(__name__)

class SQLiteDB:
    """
    SQLite 数据库操作类

    提供对 FindU 系统所需的所有数据库操作接口。
    采用线程安全的设计，支持多线程并发访问。

    Attributes:
        db_path: 数据库文件路径
        _local: 线程本地存储，用于管理每个线程的数据库连接
    """

    def __init__(self, db_path: str = None):
        """
        初始化 SQLite 连接

        Args:
            db_path: 数据库文件路径，默认为 data/findu.db
        """
        try:
            # 设置数据库文件路径
            if db_path is None:
                db_path = os.getenv("SQLITE_DB_PATH", "data/findu.db")
            
            self.db_path = db_path
            
            # 确保数据库目录存在
            db_dir = Path(self.db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
            
            # 线程本地存储，每个线程维护自己的数据库连接
            self._local = threading.local()
            
            # 初始化数据库表结构
            self._init_database()
            
            logger.info(f"SQLite 数据库初始化成功: {self.db_path}")

        except Exception as e:
            logger.error(f"SQLite 数据库初始化失败: {str(e)}")
            raise

    def _get_connection(self) -> sqlite3.Connection:
        """
        获取当前线程的数据库连接

        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            # 设置行工厂，使查询结果可以通过列名访问
            self._local.connection.row_factory = sqlite3.Row
        
        return self._local.connection

    def _init_database(self):
        """
        初始化数据库表结构

        创建所有必需的表，如果表已存在则跳过。
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        
        try:
            # 创建 prompts 表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS prompts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    prompt TEXT NOT NULL,
                    locale TEXT NOT NULL DEFAULT 'en',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'processed'
                )
            """)
            
            # 创建 cases 表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS cases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prompt_id INTEGER,
                    case_id INTEGER,
                    title TEXT,
                    description TEXT,
                    details TEXT,  -- JSON 格式存储详细信息
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (prompt_id) REFERENCES prompts (id)
                )
            """)
            
            # 创建 documents 表（预留）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER,
                    document_url TEXT NOT NULL,
                    locale TEXT NOT NULL DEFAULT 'en',
                    download_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES cases (id)
                )
            """)
            
            # 创建 users 表（预留）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT UNIQUE NOT NULL,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引以提高查询性能
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts (user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_prompts_created_at ON prompts (created_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cases_prompt_id ON cases (prompt_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_cases_created_at ON cases (created_at)")
            
            conn.commit()
            logger.info("数据库表结构初始化完成")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"初始化数据库表结构失败: {str(e)}")
            raise
        finally:
            cursor.close()

    def save_prompt(self, user_id: str, prompt: str, locale: str) -> Optional[str]:
        """
        保存用户输入的提示词

        将用户的需求提示词存储到数据库中，用于后续的数据分析和用户行为追踪。

        Args:
            user_id (str): 用户唯一标识符
            prompt (str): 用户输入的需求描述
            locale (str): 语言代码

        Returns:
            Optional[str]: 如果保存成功返回记录 ID，失败返回 None

        Example:
            >>> db = SQLiteDB()
            >>> prompt_id = db.save_prompt("user123", "我需要一个电商网站", "zh")
            >>> print(f"提示词已保存，ID: {prompt_id}")
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO prompts (user_id, prompt, locale, created_at, status)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, prompt, locale, datetime.utcnow().isoformat(), 'processed'))

            conn.commit()
            prompt_id = cursor.lastrowid
            logger.info(f"提示词已保存，ID: {prompt_id}")
            return str(prompt_id)

        except Exception as e:
            conn.rollback()
            logger.error(f"保存提示词失败: {str(e)}")
            return None
        finally:
            cursor.close()

    def save_case(self, prompt_id: str, case_data: dict) -> Optional[str]:
        """
        保存 AI 生成的案例数据

        将 AI 服务生成的案例信息存储到数据库中，建立与原始提示词的关联。

        Args:
            prompt_id (str): 关联的提示词 ID
            case_data (dict): 案例数据，包含 title, description, details 等字段

        Returns:
            Optional[str]: 如果保存成功返回记录 ID，失败返回 None

        Example:
            >>> case = {
            ...     "id": 0,
            ...     "title": "电商网站",
            ...     "description": "在线购物平台",
            ...     "details": ["用户注册", "商品展示", "购物车"]
            ... }
            >>> case_id = db.save_case("1", case)
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 提取案例数据字段
            case_id = case_data.get('id', 0)
            title = case_data.get('title', '')
            description = case_data.get('description', '')
            details = json.dumps(case_data.get('details', []), ensure_ascii=False)

            cursor.execute("""
                INSERT INTO cases (prompt_id, case_id, title, description, details, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (int(prompt_id), case_id, title, description, details, datetime.utcnow().isoformat()))

            conn.commit()
            record_id = cursor.lastrowid
            logger.info(f"案例已保存，ID: {record_id}")
            return str(record_id)

        except Exception as e:
            conn.rollback()
            logger.error(f"保存案例失败: {str(e)}")
            return None
        finally:
            cursor.close()

    def save_document_record(self, case_id: str, document_url: str, locale: str) -> Optional[str]:
        """
        保存文档生成记录

        记录用户生成的需求文档信息，用于统计和管理。

        Args:
            case_id (str): 关联的案例 ID
            document_url (str): 生成的文档 URL
            locale (str): 文档语言

        Returns:
            Optional[str]: 如果保存成功返回记录 ID，失败返回 None

        Note:
            该功能为预留接口，当前 MVP 版本暂未启用
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                INSERT INTO documents (case_id, document_url, locale, download_count, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (int(case_id), document_url, locale, 0, datetime.utcnow().isoformat()))

            conn.commit()
            record_id = cursor.lastrowid
            logger.info(f"文档记录已保存，ID: {record_id}")
            return str(record_id)

        except Exception as e:
            conn.rollback()
            logger.error(f"保存文档记录失败: {str(e)}")
            return None
        finally:
            cursor.close()

    def get_user_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """
        获取用户历史记录

        查询用户的历史提示词和生成的案例，用于个性化推荐。

        Args:
            user_id (str): 用户 ID
            limit (int): 返回记录数量限制

        Returns:
            List[Dict]: 用户历史记录列表

        Note:
            该功能为预留接口，用于未来的用户个性化功能
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("""
                SELECT id, user_id, prompt, locale, created_at, status
                FROM prompts
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (user_id, limit))

            rows = cursor.fetchall()

            # 转换为字典列表，保持与 MongoDB 版本的兼容性
            result = []
            for row in rows:
                result.append({
                    '_id': str(row['id']),
                    'user_id': row['user_id'],
                    'prompt': row['prompt'],
                    'locale': row['locale'],
                    'created_at': row['created_at'],
                    'status': row['status']
                })

            return result

        except Exception as e:
            logger.error(f"获取用户历史失败: {str(e)}")
            return []
        finally:
            cursor.close()

    def get_popular_cases(self, locale: str = "en", limit: int = 5) -> List[Dict]:
        """
        获取热门案例

        查询最受欢迎的案例类型，用于首页推荐。

        Args:
            locale (str): 语言代码
            limit (int): 返回案例数量

        Returns:
            List[Dict]: 热门案例列表

        Note:
            该功能为预留接口，用于未来的案例推荐功能
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # 查询最近创建的案例作为热门案例
            cursor.execute("""
                SELECT c.id, c.prompt_id, c.case_id, c.title, c.description, c.details, c.created_at
                FROM cases c
                JOIN prompts p ON c.prompt_id = p.id
                WHERE p.locale = ?
                ORDER BY c.created_at DESC
                LIMIT ?
            """, (locale, limit))

            rows = cursor.fetchall()

            # 转换为字典列表，保持与 MongoDB 版本的兼容性
            result = []
            for row in rows:
                details = json.loads(row['details']) if row['details'] else []
                result.append({
                    '_id': str(row['id']),
                    'prompt_id': str(row['prompt_id']),
                    'id': row['case_id'],
                    'title': row['title'],
                    'description': row['description'],
                    'details': details,
                    'created_at': row['created_at']
                })

            return result

        except Exception as e:
            logger.error(f"获取热门案例失败: {str(e)}")
            return []
        finally:
            cursor.close()

    def close(self):
        """
        关闭数据库连接

        清理当前线程的数据库连接资源。
        """
        if hasattr(self._local, 'connection'):
            try:
                self._local.connection.close()
                logger.info("SQLite 数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭 SQLite 数据库连接失败: {str(e)}")
            finally:
                delattr(self._local, 'connection')


# 创建全局数据库实例
# 替代原有的 MongoDB 实例，提供相同的接口
sqlite_db = SQLiteDB()
