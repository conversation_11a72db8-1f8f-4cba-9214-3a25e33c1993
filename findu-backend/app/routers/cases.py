"""
案例生成路由模块

该模块负责处理与需求案例生成相关的 API 端点。
主要功能包括：
- 接收用户输入的需求提示词
- 调用 AI 服务生成多个相关案例
- 返回结构化的案例数据

API 端点：
- POST /api/generate-cases: 根据提示词生成需求案例

数据流程：
用户提示词 → AI 服务处理 → 结构化案例数据 → JSON 响应

该模块是 FindU 系统的核心功能之一，为用户提供多样化的项目案例选择。
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from ..services.ai_service import get_ai_service, AIServiceError, AIAPIError, AIResponseError
from typing import List
import logging

logger = logging.getLogger(__name__)

# 创建路由器实例，用于组织相关的 API 端点
router = APIRouter()

class PromptRequest(BaseModel):
    """
    案例生成请求模型

    定义客户端发送案例生成请求时需要的参数结构。
    用于 FastAPI 的自动请求验证和 API 文档生成。

    Attributes:
        prompt (str): 用户输入的需求描述或提示词
        locale (str): 目标语言代码，默认为英文 "en"，支持 "zh" 中文
    """
    prompt: str
    locale: str = "en"

class CaseResponse(BaseModel):
    """
    案例响应数据模型

    定义单个案例的数据结构，用于类型验证和 API 文档生成。
    每个案例包含基本信息和详细需求列表。

    Attributes:
        id (int): 案例的唯一标识符
        title (str): 案例标题，简洁明了地概括项目类型
        description (str): 案例描述，一句话说明项目的主要目的
        details (List[str]): 详细需求列表，包含具体的功能点或特性
    """
    id: int
    title: str
    description: str
    details: List[str]

@router.post("/generate-cases", response_model=dict)
async def generate_cases(request: PromptRequest):
    """
    生成需求案例 API 端点

    根据用户提供的需求提示词，调用 AI 服务生成多个相关的项目案例。
    每个案例包含标题、描述和详细需求列表，为用户提供多样化选择。

    Args:
        request (PromptRequest): 包含用户提示词和语言偏好的请求对象

    Returns:
        dict: 包含案例列表的响应字典，格式为 {"cases": [案例数组]}

    Raises:
        HTTPException: 当 AI 服务调用失败或其他错误发生时抛出 500 错误

    Example:
        请求体:
        {
            "prompt": "我想要一个电商网站",
            "locale": "zh"
        }

        响应:
        {
            "cases": [
                {
                    "id": 0,
                    "title": "电子商务网站",
                    "description": "功能完整的在线购物平台",
                    "details": ["用户注册登录", "商品展示目录", ...]
                }
            ]
        }
    """
    try:
        # 调用 AI 服务生成案例
        cases = await get_ai_service().generate_cases(request.prompt, request.locale)
        return {"cases": cases}
    except AIAPIError as e:
        # API调用错误（如认证失败、模型不存在等）
        logger.error(f"AI API调用失败: {str(e)}")
        error_detail = {
            "error": "ai_api_error",
            "message": f"AI服务调用失败: {str(e)}",
            "provider": e.provider,
            "status_code": e.status_code
        }
        raise HTTPException(status_code=503, detail=error_detail)
    except AIResponseError as e:
        # AI响应格式错误
        logger.error(f"AI响应格式错误: {str(e)}")
        error_detail = {
            "error": "ai_response_error",
            "message": f"AI返回数据格式错误: {str(e)}"
        }
        raise HTTPException(status_code=422, detail=error_detail)
    except AIServiceError as e:
        # 其他AI服务错误
        logger.error(f"AI服务错误: {str(e)}")
        error_detail = {
            "error": "ai_service_error",
            "message": f"AI服务错误: {str(e)}"
        }
        raise HTTPException(status_code=500, detail=error_detail)
    except Exception as e:
        # 未知错误
        logger.error(f"生成案例时发生未知错误: {str(e)}")
        error_detail = {
            "error": "unknown_error",
            "message": f"生成案例失败: {str(e)}"
        }
        raise HTTPException(status_code=500, detail=error_detail)