"""
静态文件管理路由模块

该模块提供静态文件（主要是需求文档）的管理功能，包括：
- 文件列表查询
- 安全文件下载
- 文件删除管理
- 文件上传（可选）

安全特性：
- 只允许访问指定类型的文件（.txt）
- 防止目录遍历攻击
- 文件名格式验证
- 完整的错误处理和日志记录

技术实现：
- FastAPI Router 模块化路由
- pathlib 安全路径处理
- 异步文件操作
- RESTful API 设计
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, UploadFile, File, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器实例
router = APIRouter(
    prefix="/files",
    tags=["Static Files"],
    responses={404: {"description": "文件未找到"}}
)

# 支持的文件类型配置
ALLOWED_EXTENSIONS = {'.txt', '.md', '.pdf', '.docx'}
ALLOWED_MIME_TYPES = {
    '.txt': 'text/plain; charset=utf-8',
    '.md': 'text/markdown; charset=utf-8', 
    '.pdf': 'application/pdf',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
}

class FileInfo(BaseModel):
    """文件信息数据模型"""
    name: str
    size: int
    modified: float
    modified_readable: str
    extension: str
    url: str
    download_url: str

class FileListResponse(BaseModel):
    """文件列表响应模型"""
    files: List[FileInfo]
    total: int
    directory: str
    allowed_extensions: List[str]

class FileOperationResponse(BaseModel):
    """文件操作响应模型"""
    success: bool
    message: str
    filename: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

def get_static_directory() -> Path:
    """
    获取静态文件目录路径

    根据环境变量或默认配置确定静态文件存储位置。
    支持相对路径和绝对路径配置。

    Returns:
        Path: 静态文件目录的路径对象
    """
    static_path = os.getenv("STATIC_FILES_PATH", "static/demands")
    static_dir = Path(static_path).resolve()
    
    # 确保目录存在
    if not static_dir.exists():
        try:
            static_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"创建静态文件目录: {static_dir}")
        except Exception as e:
            logger.error(f"创建静态文件目录失败: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"无法创建静态文件目录: {str(e)}"
            )
    
    return static_dir

def validate_filename(filename: str, check_extension: bool = True) -> str:
    """
    验证文件名的安全性
    
    Args:
        filename: 要验证的文件名
        check_extension: 是否检查文件扩展名
        
    Returns:
        str: 验证通过的文件名
        
    Raises:
        HTTPException: 文件名不安全或格式不正确时抛出异常
    """
    # 基础安全检查
    if not filename or not filename.strip():
        raise HTTPException(
            status_code=400,
            detail="文件名不能为空"
        )
    
    filename = filename.strip()
    
    # 防止目录遍历攻击
    dangerous_patterns = ['..',  '/', '\\', ':', '*', '?', '"', '<', '>', '|']
    if any(pattern in filename for pattern in dangerous_patterns):
        raise HTTPException(
            status_code=400,
            detail="文件名包含不安全字符"
        )
    
    # 检查文件扩展名
    if check_extension:
        file_ext = Path(filename).suffix.lower()
        if file_ext not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型。允许的类型: {', '.join(ALLOWED_EXTENSIONS)}"
            )
    
    return filename

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小为人类可读格式
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

@router.get("/list", response_model=FileListResponse)
async def list_files(
    extension: Optional[str] = Query(None, description="按文件扩展名过滤 (如: .txt)"),
    limit: Optional[int] = Query(None, ge=1, le=1000, description="返回文件数量限制"),
    sort_by: str = Query("modified", description="排序字段: name, size, modified"),
    sort_order: str = Query("desc", description="排序顺序: asc, desc")
):
    """
    列出所有可用的文件
    
    扫描静态文件目录，返回所有可访问的文件列表。
    支持按扩展名过滤、分页和排序功能。
    
    Args:
        extension: 可选的文件扩展名过滤器
        limit: 返回结果数量限制
        sort_by: 排序字段
        sort_order: 排序顺序
        
    Returns:
        FileListResponse: 包含文件列表和元信息的响应
        
    Raises:
        HTTPException: 当静态文件目录不存在或访问失败时
    """
    static_dir = get_static_directory()
    
    try:
        files = []
        
        # 扫描文件
        for file_path in static_dir.iterdir():
            if not file_path.is_file():
                continue
                
            file_ext = file_path.suffix.lower()
            
            # 只处理允许的文件类型
            if file_ext not in ALLOWED_EXTENSIONS:
                continue
                
            # 扩展名过滤
            if extension and file_ext != extension.lower():
                continue
            
            try:
                # 获取文件信息
                stat_info = file_path.stat()
                modified_time = stat_info.st_mtime
                
                file_info = FileInfo(
                    name=file_path.name,
                    size=stat_info.st_size,
                    modified=modified_time,
                    modified_readable=datetime.fromtimestamp(modified_time).strftime("%Y-%m-%d %H:%M:%S"),
                    extension=file_ext,
                    url=f"/static/demands/{file_path.name}",
                    download_url=f"/api/files/download/{file_path.name}"
                )
                
                files.append(file_info)
                
            except Exception as e:
                logger.warning(f"获取文件信息失败 {file_path.name}: {str(e)}")
                continue
        
        # 排序
        reverse = (sort_order.lower() == "desc")
        if sort_by == "name":
            files.sort(key=lambda x: x.name.lower(), reverse=reverse)
        elif sort_by == "size":
            files.sort(key=lambda x: x.size, reverse=reverse)
        elif sort_by == "modified":
            files.sort(key=lambda x: x.modified, reverse=reverse)
        
        # 限制结果数量
        if limit:
            files = files[:limit]
        
        return FileListResponse(
            files=files,
            total=len(files),
            directory=str(static_dir),
            allowed_extensions=list(ALLOWED_EXTENSIONS)
        )
        
    except Exception as e:
        logger.error(f"扫描文件目录失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"扫描文件目录失败: {str(e)}"
        )

@router.get("/download/{filename}")
async def download_file(filename: str):
    """
    下载指定文件
    
    提供安全的文件下载服务，支持多种文件类型。
    自动设置正确的 MIME 类型和下载头。
    
    Args:
        filename: 要下载的文件名
        
    Returns:
        FileResponse: 文件下载响应
        
    Raises:
        HTTPException: 文件不存在、格式不正确或访问被拒绝时
    """
    # 验证文件名安全性
    filename = validate_filename(filename)
    
    static_dir = get_static_directory()
    file_path = static_dir / filename
    
    # 检查文件是否存在
    if not file_path.exists() or not file_path.is_file():
        raise HTTPException(
            status_code=404,
            detail=f"文件不存在: {filename}"
        )
    
    # 获取文件扩展名和对应的 MIME 类型
    file_ext = file_path.suffix.lower()
    media_type = ALLOWED_MIME_TYPES.get(file_ext, 'application/octet-stream')
    
    try:
        # 记录下载日志
        logger.info(f"文件下载: {filename} ({format_file_size(file_path.stat().st_size)})")
        
        # 返回文件下载响应
        return FileResponse(
            path=str(file_path),
            filename=filename,
            media_type=media_type,
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{filename}",
                "Cache-Control": "no-cache"
            }
        )
        
    except Exception as e:
        logger.error(f"文件下载失败 {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件下载失败: {str(e)}"
        )

@router.delete("/delete/{filename}", response_model=FileOperationResponse)
async def delete_file(filename: str):
    """
    删除指定文件
    
    提供安全的文件删除服务。
    注意：此操作不可逆，删除前请确认。
    
    Args:
        filename: 要删除的文件名
        
    Returns:
        FileOperationResponse: 删除结果信息
        
    Raises:
        HTTPException: 文件不存在、格式不正确或删除失败时
    """
    # 验证文件名安全性
    filename = validate_filename(filename)
    
    static_dir = get_static_directory()
    file_path = static_dir / filename
    
    # 检查文件是否存在
    if not file_path.exists() or not file_path.is_file():
        raise HTTPException(
            status_code=404,
            detail=f"文件不存在: {filename}"
        )
    
    try:
        # 获取文件信息（删除前记录）
        file_size = file_path.stat().st_size
        
        # 删除文件
        file_path.unlink()
        
        logger.info(f"文件已删除: {filename} ({format_file_size(file_size)})")
        
        return FileOperationResponse(
            success=True,
            message=f"文件 {filename} 已成功删除",
            filename=filename,
            details={
                "deleted_size": file_size,
                "deleted_size_readable": format_file_size(file_size)
            }
        )
        
    except Exception as e:
        logger.error(f"删除文件失败 {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"删除文件失败: {str(e)}"
        )

@router.post("/upload", response_model=FileOperationResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    上传文件到静态目录
    
    支持上传多种类型的文件到静态文件目录。
    自动验证文件类型和大小。
    
    Args:
        file: 上传的文件对象
        
    Returns:
        FileOperationResponse: 上传结果信息
        
    Raises:
        HTTPException: 文件类型不支持、文件过大或上传失败时
    """
    # 验证文件名
    if not file.filename:
        raise HTTPException(
            status_code=400,
            detail="文件名不能为空"
        )
    
    filename = validate_filename(file.filename)
    
    # 检查文件大小（默认限制为 50MB）
    max_file_size = int(os.getenv("MAX_UPLOAD_SIZE", 50 * 1024 * 1024))  # 50MB
    
    static_dir = get_static_directory()
    file_path = static_dir / filename
    
    # 检查文件是否已存在
    if file_path.exists():
        raise HTTPException(
            status_code=409,
            detail=f"文件已存在: {filename}"
        )
    
    try:
        # 读取文件内容
        content = await file.read()
        
        # 检查文件大小
        if len(content) > max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件过大。最大允许大小: {format_file_size(max_file_size)}"
            )
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(content)
        
        logger.info(f"文件上传成功: {filename} ({format_file_size(len(content))})")
        
        return FileOperationResponse(
            success=True,
            message=f"文件 {filename} 上传成功",
            filename=filename,
            details={
                "size": len(content),
                "size_readable": format_file_size(len(content)),
                "url": f"/static/demands/{filename}",
                "download_url": f"/api/files/download/{filename}"
            }
        )
        
    except HTTPException:
        raise  # 重新抛出 HTTP 异常
    except Exception as e:
        # 清理可能创建的文件
        if file_path.exists():
            try:
                file_path.unlink()
            except:
                pass
                
        logger.error(f"文件上传失败 {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )

@router.get("/info/{filename}")
async def get_file_info(filename: str):
    """
    获取指定文件的详细信息
    
    Args:
        filename: 文件名
        
    Returns:
        dict: 文件详细信息
        
    Raises:
        HTTPException: 文件不存在时
    """
    # 验证文件名安全性
    filename = validate_filename(filename)
    
    static_dir = get_static_directory()
    file_path = static_dir / filename
    
    if not file_path.exists() or not file_path.is_file():
        raise HTTPException(
            status_code=404,
            detail=f"文件不存在: {filename}"
        )
    
    try:
        stat_info = file_path.stat()
        
        return {
            "name": file_path.name,
            "size": stat_info.st_size,
            "size_readable": format_file_size(stat_info.st_size),
            "extension": file_path.suffix.lower(),
            "created": stat_info.st_ctime,
            "modified": stat_info.st_mtime,
            "created_readable": datetime.fromtimestamp(stat_info.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
            "modified_readable": datetime.fromtimestamp(stat_info.st_mtime).strftime("%Y-%m-%d %H:%M:%S"),
            "url": f"/static/demands/{filename}",
            "download_url": f"/api/files/download/{filename}",
            "mime_type": ALLOWED_MIME_TYPES.get(file_path.suffix.lower(), 'application/octet-stream')
        }
        
    except Exception as e:
        logger.error(f"获取文件信息失败 {filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文件信息失败: {str(e)}"
        )