from typing import List, Dict, Any

def _generate_mock_cases(prompt: str, locale: str) -> List[Dict[str, Any]]:
    """生成模拟案例数据"""
    if locale == "zh":
        base_cases = [
            {
                "id": 0,
                "title": "电子商务网站",
                "description": "功能完整的在线购物平台",
                "details": ["用户注册登录", "商品展示目录", "购物车功能", "在线支付", "订单管理系统"]
            },
            {
                "id": 1,
                "title": "企业官网",
                "description": "专业的企业形象展示网站",
                "details": ["公司介绍页面", "产品服务展示", "新闻资讯模块", "联系我们页面", "响应式设计"]
            },
            {
                "id": 2,
                "title": "内容管理系统",
                "description": "灵活的内容发布和管理平台",
                "details": ["文章发布系统", "用户权限管理", "评论互动功能", "搜索功能", "数据统计分析"]
            }
        ]
    else:
        base_cases = [
            {
                "id": 0,
                "title": "E-commerce Website",
                "description": "Full-featured online shopping platform",
                "details": ["User registration", "Product catalog", "Shopping cart", "Payment integration", "Order management"]
            },
            {
                "id": 1,
                "title": "Corporate Website",
                "description": "Professional business showcase website",
                "details": ["Company introduction", "Product showcase", "News section", "Contact page", "Responsive design"]
            },
            {
                "id": 2,
                "title": "Content Management System",
                "description": "Flexible content publishing platform",
                "details": ["Article publishing", "User management", "Comment system", "Search functionality", "Analytics dashboard"]
            }
        ]

    # 根据用户输入调整案例内容
    if "移动" in prompt or "mobile" in prompt.lower():
        base_cases[0]["title"] = "移动应用" if locale == "zh" else "Mobile App"
        base_cases[0]["description"] = "跨平台移动应用程序" if locale == "zh" else "Cross-platform mobile application"

    return base_cases

def _generate_mock_document(case_id: int, locale: str) -> str:
    """生成模拟需求文档"""
    if locale == "zh":
        return f"""

        # 项目需求文档

        ## 项目背景
        基于用户选择的案例 #{case_id}，我们需要开发一个现代化的数字解决方案。该项目旨在满足当前市场需求，提供优质的用户体验。

        ## 项目目标
        - 构建用户友好的界面
        - 实现核心业务功能
        - 确保系统稳定性和安全性
        - 提供良好的性能表现

        ## 功能需求

        ### 核心功能
        1. **用户管理系统**
        - 用户注册和登录
        - 个人信息管理
        - 权限控制

        2. **主要业务功能**
        - 数据展示和管理
        - 交互操作界面
        - 实时更新机制

        3. **系统管理**
        - 后台管理界面
        - 数据统计分析
        - 系统监控

        ## 技术要求
        - **前端**: React/Vue.js + TypeScript
        - **后端**: Node.js/Python + 数据库
        - **部署**: 云服务器 + CDN
        - **安全**: HTTPS + 数据加密

        ## 预算范围
        - 开发成本: 5-15万元
        - 运维成本: 每月1000-3000元
        - 第三方服务: 根据实际使用量

        ## 时间计划
        - **需求分析**: 1-2周
        - **设计阶段**: 2-3周
        - **开发阶段**: 6-10周
        - **测试部署**: 2-3周
        - **总计**: 3-4个月

        ## 交付物
        - 完整的源代码
        - 部署文档
        - 用户使用手册
        - 技术维护文档

        ---
        *本文档由AI需求生成器自动生成，请根据实际情况进行调整。*
        """
    
    else:
        return f"""
        # Project Requirements Document

        ## Project Background
        Based on the selected case #{case_id}, we need to develop a modern digital solution. This project aims to meet current market demands and provide excellent user experience.

        ## Project Objectives
        - Build user-friendly interface
        - Implement core business functions
        - Ensure system stability and security
        - Provide good performance

        ## Functional Requirements

        ### Core Features
        1. **User Management System**
        - User registration and login
        - Profile management
        - Permission control

        2. **Main Business Functions**
        - Data display and management
        - Interactive operation interface
        - Real-time update mechanism

        3. **System Administration**
        - Admin dashboard
        - Data analytics
        - System monitoring

        ## Technical Requirements
        - **Frontend**: React/Vue.js + TypeScript
        - **Backend**: Node.js/Python + Database
        - **Deployment**: Cloud server + CDN
        - **Security**: HTTPS + Data encryption

        ## Budget Range
        - Development cost: $7,000 - $20,000
        - Operation cost: $150 - $400/month
        - Third-party services: Based on usage

        ## Timeline
        - **Requirements Analysis**: 1-2 weeks
        - **Design Phase**: 2-3 weeks
        - **Development Phase**: 6-10 weeks
        - **Testing & Deployment**: 2-3 weeks
        - **Total**: 3-4 months

        ## Deliverables
        - Complete source code
        - Deployment documentation
        - User manual
        - Technical maintenance guide

        ---
        *This document is automatically generated by AI Demand Generator. Please adjust according to actual requirements.*
        """