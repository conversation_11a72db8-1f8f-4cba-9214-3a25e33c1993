"""
AI服务模块

该模块提供统一的AI服务接口，兼容所有符合OpenAI API规范的模型服务，
包括但不限于：OpenAI、通义千问、文心一言、智谱AI、Moonshot等。
"""

import os
import json
import asyncio
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import logging

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletion

from .ai_mocks import _generate_mock_cases, _generate_mock_document

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ServiceType(Enum):
    """服务类型枚举"""
    CASES = "cases"
    DOCUMENT = "document"

# 常用AI服务提供商配置预设
AI_PROVIDERS = {
    "openai": {
        "base_url": "https://api.openai.com/v1",
        "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"],
        "default_model": "gpt-4-turbo"
    },
    "qwen": {
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "models": ["qwen-turbo", "qwen-plus", "qwen-max", "qwen-max-longcontext"],
        "default_model": "qwen-turbo"
    },
    "moonshot": {
        "base_url": "https://api.moonshot.cn/v1",
        "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
        "default_model": "moonshot-v1-8k"
    },
    "zhipu": {
        "base_url": "https://open.bigmodel.cn/api/paas/v4",
        "models": ["glm-4", "glm-4-air", "glm-4-airx", "glm-4-flash"],
        "default_model": "glm-4"
    },
    "deepseek": {
        "base_url": "https://api.deepseek.com/v1",
        "models": ["deepseek-chat", "deepseek-coder"],
        "default_model": "deepseek-chat"
    },
    "baichuan": {
        "base_url": "https://api.baichuan-ai.com/v1",
        "models": ["Baichuan2-Turbo", "Baichuan2-Turbo-192k"],
        "default_model": "Baichuan2-Turbo"
    },
    "custom": {
        "base_url": None,  # 用户自定义
        "models": [],
        "default_model": None
    }
}

@dataclass
class AIConfig:
    """AI服务配置"""
    api_key: str
    base_url: str
    model: str
    provider: str = "custom"
    timeout: float = 30.0
    max_retries: int = 3
    temperature: float = 0.7
    max_tokens: int = 3000
    top_p: float = 0.8
    frequency_penalty: float = 0.1
    presence_penalty: float = 0.1
    extra_headers: Dict[str, str] = field(default_factory=dict)
    
    @classmethod
    def from_provider(cls, provider: str, api_key: str, model: Optional[str] = None, **kwargs) -> "AIConfig":
        """
        根据预设的服务提供商创建配置
        
        Args:
            provider: 服务提供商名称
            api_key: API密钥
            model: 模型名称，如果为None则使用默认模型
            **kwargs: 其他配置参数
        """
        if provider not in AI_PROVIDERS:
            raise ValueError(f"不支持的服务提供商: {provider}")
        
        provider_config = AI_PROVIDERS[provider]
        model = model or provider_config["default_model"]
        
        if provider_config["models"] and model not in provider_config["models"]:
            logger.warning(f"模型 {model} 可能不被 {provider} 支持")
        
        return cls(
            provider=provider,
            api_key=api_key,
            base_url=provider_config["base_url"],
            model=model,
            **kwargs
        )

@dataclass
class CaseData:
    """案例数据结构"""
    id: int
    title: str
    description: str
    details: List[str]

class AIServiceError(Exception):
    """AI服务异常基类"""
    pass

class AIAPIError(AIServiceError):
    """API调用异常"""
    def __init__(self, message: str, status_code: Optional[int] = None, provider: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.provider = provider

class AIResponseError(AIServiceError):
    """响应解析异常"""
    pass

class AIService:
    """AI服务客户端 - 兼容所有OpenAI规范的AI服务"""
    
    def __init__(self, config: Optional[AIConfig] = None):
        """
        初始化AI服务客户端
        
        Args:
            config: AI服务配置，如果为None则从环境变量读取
        """
        if config is None:
            config = self._load_config_from_env()
        
        self.config = config
        self._client: Optional[AsyncOpenAI] = None
        
        logger.info(f"初始化AI服务: {config.provider} - {config.model}")
        
    def _load_config_from_env(self) -> AIConfig:
        """从环境变量加载配置"""
        # 优先检查是否指定了服务提供商
        provider = os.getenv('AI_PROVIDER', 'custom').lower()
        api_key = os.getenv('AI_API_KEY', os.getenv('OPENAI_API_KEY', ''))
        
        if provider != 'custom' and provider in AI_PROVIDERS:
            # 使用预设的服务提供商配置
            model = os.getenv('AI_MODEL', AI_PROVIDERS[provider]["default_model"])
            return AIConfig.from_provider(
                provider=provider,
                api_key=api_key,
                model=model,
                timeout=float(os.getenv('AI_TIMEOUT', '30')),
                max_retries=int(os.getenv('AI_MAX_RETRIES', '3')),
                temperature=float(os.getenv('AI_TEMPERATURE', '0.7')),
                max_tokens=int(os.getenv('AI_MAX_TOKENS', '3000'))
            )
        else:
            # 使用自定义配置
            base_url = os.getenv('AI_BASE_URL', os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1'))
            model = os.getenv('AI_MODEL', os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo'))
            
            return AIConfig(
                provider='custom',
                api_key=api_key,
                base_url=base_url,
                model=model,
                timeout=float(os.getenv('AI_TIMEOUT', '30')),
                max_retries=int(os.getenv('AI_MAX_RETRIES', '3')),
                temperature=float(os.getenv('AI_TEMPERATURE', '0.7')),
                max_tokens=int(os.getenv('AI_MAX_TOKENS', '3000'))
            )
    
    @property
    def client(self) -> AsyncOpenAI:
        """获取OpenAI兼容客户端实例"""
        if self._client is None:
            # 构建客户端参数
            client_kwargs = {
                "api_key": self.config.api_key,
                "base_url": self.config.base_url,
                "timeout": self.config.timeout,
                "max_retries": self.config.max_retries
            }
            
            # 添加额外的请求头（某些服务商需要特殊头部）
            if self.config.extra_headers:
                client_kwargs["default_headers"] = self.config.extra_headers
            
            self._client = AsyncOpenAI(**client_kwargs)
            
        return self._client
    
    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.close()
            self._client = None
    
    def _is_api_available(self) -> bool:
        """检查API是否可用"""
        return bool(
            self.config.api_key and 
            self.config.api_key.strip() and
            self.config.api_key not in ['your_api_key_here', 'sk-xxx', 'your-api-key']
        )
    
    def _build_system_prompt(self, service_type: ServiceType, locale: str) -> str:
        """构建系统提示词"""
        language = "中文" if locale == "zh" else "English"
        
        if service_type == ServiceType.CASES:
            return f"""你是一个专业的需求分析师。根据用户的需求描述，生成3个具体的项目案例。

每个案例需要包含：
1. 标题（简洁明了，不超过20字）
2. 描述（一句话概括，突出核心价值）  
3. 详细需求列表（3-5个具体功能点，每个功能点要具体可执行）

要求：
- 案例要有层次感，从基础到高级
- 每个案例要有明确的目标用户群体
- 需求要符合实际业务场景
- 确保每个案例都有独特性，避免重复

请用{language}回复，严格按照以下JSON格式返回：
[
  {{
    "id": 0,
    "title": "案例标题",
    "description": "案例描述", 
    "details": ["需求1", "需求2", "需求3", "需求4", "需求5"]
  }},
  {{
    "id": 1,
    "title": "案例标题",
    "description": "案例描述",
    "details": ["需求1", "需求2", "需求3", "需求4"]
  }},
  {{
    "id": 2, 
    "title": "案例标题",
    "description": "案例描述",
    "details": ["需求1", "需求2", "需求3", "需求4", "需求5"]
  }}
]

注意：只返回JSON数组，不要包含任何其他文字说明或markdown格式。"""

        else:  # DOCUMENT
            return f"""你是一个专业的技术文档编写专家。根据项目案例，生成详细的需求文档。

文档必须包含以下结构：

# 项目需求文档

## 1. 项目概述
- 项目背景与起因
- 项目愿景与使命
- 目标用户画像

## 2. 需求分析
- 业务需求梳理
- 用户需求分析
- 功能需求优先级

## 3. 功能规格说明
- 核心功能详细描述
- 功能流程图说明
- 用户交互设计要求

## 4. 技术规格要求
- 推荐技术架构
- 性能指标要求
- 安全性要求
- 可扩展性考虑

## 5. 项目实施计划
- 开发阶段划分
- 关键里程碑
- 交付时间计划

## 6. 资源与预算
- 人力资源需求
- 技术资源需求
- 预算范围估算

## 7. 风险评估与应对
- 主要风险识别
- 风险影响评估
- 风险应对策略

## 8. 验收标准
- 功能验收标准
- 性能验收标准
- 用户体验验收标准

请用{language}回复，使用标准Markdown格式，确保内容专业、详细、具有可操作性。每个章节都要有具体的内容，不要使用占位符。"""

    async def _make_chat_completion(self, messages: List[Dict[str, str]]) -> str:
        """
        调用OpenAI兼容的chat completion接口

        Args:
            messages: 消息列表

        Returns:
            AI响应内容

        Raises:
            AIAPIError: API调用失败
        """
        last_exception = None

        # 实现重试机制
        for attempt in range(self.config.max_retries):
            try:
                logger.info(f"发起Chat Completion请求 (尝试 {attempt + 1}/{self.config.max_retries}) - Provider: {self.config.provider}, Model: {self.config.model}")

                # 构建请求参数
                request_params = {
                    "model": self.config.model,
                    "messages": messages,
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_tokens,
                    "top_p": self.config.top_p,
                    "frequency_penalty": self.config.frequency_penalty,
                    "presence_penalty": self.config.presence_penalty,
                    "stream": False
                }

                # 某些提供商可能不支持所有参数，进行兼容性处理
                if self.config.provider in ["qwen", "zhipu"]:
                    # 通义千问和智谱AI可能不支持某些参数
                    request_params.pop("frequency_penalty", None)
                    request_params.pop("presence_penalty", None)

                # 使用asyncio.wait_for添加额外的超时保护
                completion: ChatCompletion = await asyncio.wait_for(
                    self.client.chat.completions.create(**request_params),
                    timeout=self.config.timeout
                )

                # 提取响应内容
                if not completion.choices:
                    raise AIAPIError("响应中没有choices", provider=self.config.provider)

                content = completion.choices[0].message.content
                if not content or not content.strip():
                    raise AIAPIError("AI返回空内容", provider=self.config.provider)

                # 记录token使用情况（如果有的话）
                if hasattr(completion, 'usage') and completion.usage:
                    logger.info(f"Token使用情况 - 输入: {completion.usage.prompt_tokens}, "
                              f"输出: {completion.usage.completion_tokens}, "
                              f"总计: {completion.usage.total_tokens}")

                logger.info("Chat Completion请求成功完成")
                return content.strip()

            except asyncio.TimeoutError:
                last_exception = AIAPIError(f"请求超时 ({self.config.timeout}秒)", provider=self.config.provider)
                logger.warning(f"尝试 {attempt + 1} 超时，{self.config.timeout}秒")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(min(2 ** attempt, 10))  # 指数退避，最大10秒
                    continue

            except Exception as e:
                last_exception = e
                error_msg = f"AI服务调用失败 ({self.config.provider}): {str(e)}"

                # 处理不同类型的异常
                if hasattr(e, 'status_code'):
                    # OpenAI SDK的HTTP异常
                    status_code = getattr(e, 'status_code', None)

                    if status_code == 401:
                        error_msg = f"API密钥无效 ({self.config.provider})"
                        # API密钥错误不需要重试
                        raise AIAPIError(error_msg, status_code, self.config.provider)
                    elif status_code == 429:
                        error_msg = f"请求频率超限 ({self.config.provider})"
                        logger.warning(f"尝试 {attempt + 1} 频率超限，等待重试")
                    elif status_code == 404:
                        error_msg = f"模型不存在或API端点错误 ({self.config.provider}): {self.config.model}"
                        # 模型不存在错误不需要重试
                        raise AIAPIError(error_msg, status_code, self.config.provider)
                    elif status_code >= 500:
                        error_msg = f"服务器错误 ({self.config.provider}): {status_code}"
                        logger.warning(f"尝试 {attempt + 1} 服务器错误，等待重试")

                    # 尝试获取详细错误信息
                    if hasattr(e, 'response') and e.response:
                        try:
                            error_detail = e.response.json()
                            if 'error' in error_detail and 'message' in error_detail['error']:
                                error_msg += f" - {error_detail['error']['message']}"
                        except:
                            pass
                else:
                    # 其他类型的异常
                    logger.warning(f"尝试 {attempt + 1} 发生异常: {error_msg}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.config.max_retries - 1:
                    wait_time = min(2 ** attempt, 10)  # 指数退避，最大10秒
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    continue

        # 所有重试都失败了，抛出最后一个异常
        if isinstance(last_exception, AIAPIError):
            raise last_exception
        elif hasattr(last_exception, 'status_code'):
            status_code = getattr(last_exception, 'status_code', None)
            error_msg = f"AI服务调用失败 ({self.config.provider}): {str(last_exception)}"
            raise AIAPIError(error_msg, status_code, self.config.provider)
        else:
            error_msg = f"AI服务调用失败 ({self.config.provider}): {str(last_exception)}"
            raise AIServiceError(error_msg)

    async def generate_cases(self, prompt: str, locale: str = "zh") -> List[CaseData]:
        """
        根据用户提示词生成需求案例
        
        Args:
            prompt: 用户输入的需求描述
            locale: 目标语言（'en' 或 'zh'）
            
        Returns:
            案例数据列表
        """
        # 输入验证
        if not prompt or not prompt.strip():
            raise AIServiceError("输入提示词不能为空")
        
        if locale not in ["zh", "en"]:
            logger.warning(f"不支持的语言: {locale}, 使用中文")
            locale = "zh"
        
        try:
            # 强制进行真实API调用，不使用模拟数据
            logger.info(f"开始真实AI API调用 - Provider: {self.config.provider}, Model: {self.config.model}")

            system_prompt = self._build_system_prompt(ServiceType.CASES, locale)
            user_prompt = f"用户需求：{prompt.strip()}"

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # 直接调用API，不进行回退
            content = await self._make_chat_completion(messages)
            
            # 尝试解析JSON响应
            try:
                # 清理可能的格式问题
                content = content.strip()
                logger.info(f"AI原始响应内容: {content[:200]}...")

                # 移除可能的markdown代码块标记
                if content.startswith("```json"):
                    content = content[7:]
                elif content.startswith("```"):
                    content = content[3:]
                if content.endswith("```"):
                    content = content[:-3]
                content = content.strip()

                # 移除可能的前后文本说明
                lines = content.split('\n')
                json_lines = []
                in_json = False

                for line in lines:
                    line = line.strip()
                    if line.startswith('[') or in_json:
                        in_json = True
                        json_lines.append(line)
                        if line.endswith(']') and line.count('[') <= line.count(']'):
                            break

                if json_lines:
                    content = '\n'.join(json_lines)

                # 尝试修复常见的JSON格式问题
                if not content.startswith('['):
                    # 查找第一个 [ 字符
                    start_index = content.find('[')
                    if start_index != -1:
                        content = content[start_index:]
                    else:
                        raise AIResponseError("响应中未找到JSON数组开始标记")

                if not content.endswith(']'):
                    # 查找最后一个 ] 字符
                    end_index = content.rfind(']')
                    if end_index != -1:
                        content = content[:end_index + 1]
                    else:
                        raise AIResponseError("响应中未找到JSON数组结束标记")

                # 修复常见的JSON格式错误
                content = content.replace('，', ',')  # 中文逗号替换为英文逗号
                content = content.replace('"', '"').replace('"', '"')  # 中文引号替换为英文引号
                content = content.replace(''', "'").replace(''', "'")  # 中文单引号替换为英文单引号

                logger.info(f"清理后的JSON内容: {content[:200]}...")

                cases_data = json.loads(content)
                
                # 验证数据结构
                if not isinstance(cases_data, list):
                    raise AIResponseError("响应不是数组格式")
                
                if len(cases_data) == 0:
                    raise AIResponseError("返回的案例数组为空")
                
                cases = []
                for i, case_dict in enumerate(cases_data):
                    try:
                        # 确保数据完整性
                        if not isinstance(case_dict, dict):
                            logger.warning(f"案例 {i} 不是字典格式，跳过")
                            continue
                        
                        # 设置正确的ID
                        case_dict['id'] = i
                        
                        # 验证必需字段
                        required_fields = ['title', 'description', 'details']
                        for field in required_fields:
                            if field not in case_dict or not case_dict[field]:
                                raise KeyError(f"缺少必需字段或字段为空: {field}")
                        
                        # 数据类型转换和清理
                        case_dict['title'] = str(case_dict['title']).strip()[:100]
                        case_dict['description'] = str(case_dict['description']).strip()[:300]
                        
                        # 确保details是列表
                        if not isinstance(case_dict['details'], list):
                            case_dict['details'] = [str(case_dict['details'])]
                        else:
                            case_dict['details'] = [str(detail).strip() for detail in case_dict['details'] if detail]
                        
                        # 限制details数量
                        case_dict['details'] = case_dict['details'][:8]
                        
                        if not case_dict['details']:
                            raise ValueError("需求列表为空")
                        
                        cases.append(CaseData(**case_dict))
                        
                    except (KeyError, TypeError, ValueError) as e:
                        logger.warning(f"案例 {i} 数据格式错误: {str(e)}, 跳过该案例")
                        continue
                
                if not cases:
                    raise AIResponseError("没有有效的案例数据")
                
                logger.info(f"成功生成 {len(cases)} 个案例")
                return cases
                
            except json.JSONDecodeError as e:
                error_msg = f"AI返回的内容不是有效的JSON格式: {str(e)}"
                logger.error(f"JSON解析失败 ({self.config.provider}): {error_msg}")
                raise AIResponseError(error_msg)
            except AIResponseError as e:
                logger.error(f"AI响应格式错误 ({self.config.provider}): {str(e)}")
                raise e
                
        except AIServiceError:
            raise  # 直接抛出AI服务错误
        except Exception as e:
            error_msg = f"生成案例时发生未知错误: {str(e)}"
            logger.error(f"生成案例失败 ({self.config.provider}): {error_msg}")
            raise AIServiceError(error_msg)

    async def generate_document(self, case: CaseData, locale: str = "zh") -> str:
        """
        根据案例生成需求文档
        
        Args:
            case: 案例数据
            locale: 目标语言（'en' 或 'zh'）
            
        Returns:
            Markdown格式的需求文档内容
        """
        if not case:
            raise AIServiceError("案例数据不能为空")
        
        if locale not in ["zh", "en"]:
            logger.warning(f"不支持的语言: {locale}, 使用中文")
            locale = "zh"
        
        try:
            # 强制进行真实AI API调用，不使用模拟数据
            logger.info(f"开始真实AI API调用生成文档 - Provider: {self.config.provider}, Model: {self.config.model}")

            system_prompt = self._build_system_prompt(ServiceType.DOCUMENT, locale)

            # 构建详细的用户提示
            case_info = f"""
项目案例信息：
- 项目标题：{case.title}
- 项目描述：{case.description}
- 具体功能需求：
""" + "\n".join([f"  {i+1}. {detail}" for i, detail in enumerate(case.details)])

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": case_info}
            ]

            # 直接调用API，不进行回退
            content = await self._make_chat_completion(messages)
            
            # 内容质量验证
            if len(content) < 500:  # 文档内容太短
                error_msg = f"AI返回的文档内容过短 ({len(content)} 字符)，可能生成失败"
                logger.warning(f"文档内容质量检查失败 ({self.config.provider}): {error_msg}")
                raise AIResponseError(error_msg)

            # 检查是否包含基本的文档结构
            required_sections = ['项目', '需求', '功能'] if locale == 'zh' else ['Project', 'Requirements', 'Features']
            if not any(section in content for section in required_sections):
                error_msg = "AI返回的文档缺少必要章节，可能生成失败"
                logger.warning(f"文档结构检查失败 ({self.config.provider}): {error_msg}")
                raise AIResponseError(error_msg)
            
            logger.info(f"成功生成需求文档 ({len(content)} 字符)")
            return content
            
        except AIServiceError:
            raise  # 直接抛出AI服务错误
        except Exception as e:
            error_msg = f"生成文档时发生未知错误: {str(e)}"
            logger.error(f"生成文档失败 ({self.config.provider}): {error_msg}")
            raise AIServiceError(error_msg)

# 全局AI服务实例管理
_ai_service_instance: Optional[AIService] = None

def get_ai_service() -> AIService:
    """获取AI服务单例"""
    global _ai_service_instance
    if _ai_service_instance is None:
        _ai_service_instance = AIService()
    return _ai_service_instance

async def set_ai_service_config(config: AIConfig):
    """设置AI服务配置（会重新创建实例）"""
    global _ai_service_instance
    if _ai_service_instance:
        await _ai_service_instance.close()
    _ai_service_instance = AIService(config)

def get_supported_providers() -> Dict[str, Dict[str, Any]]:
    """获取支持的AI服务提供商列表"""
    return AI_PROVIDERS.copy()

async def test_ai_service_connection(config: Optional[AIConfig] = None) -> Dict[str, Any]:
    """
    测试AI服务连接
    
    Args:
        config: 可选的配置，如果为None则使用当前配置
        
    Returns:
        测试结果字典
    """
    test_service = AIService(config) if config else get_ai_service()
    
    try:
        # 发送一个简单的测试请求
        messages = [
            {"role": "system", "content": "你是一个AI助手，请简短回答。"},
            {"role": "user", "content": "请回复'连接测试成功'"}
        ]
        
        content = await test_service._make_chat_completion(messages)
        
        return {
            "success": True,
            "provider": test_service.config.provider,
            "model": test_service.config.model,
            "response": content[:100] + "..." if len(content) > 100 else content
        }
        
    except Exception as e:
        return {
            "success": False,
            "provider": test_service.config.provider,
            "model": test_service.config.model,
            "error": str(e)
        }
    finally:
        if config:  # 如果是临时创建的服务，需要关闭
            await test_service.close()

async def cleanup_ai_service():
    """清理AI服务资源"""
    global _ai_service_instance
    if _ai_service_instance:
        await _ai_service_instance.close()
        _ai_service_instance = None