"""
文档生成服务模块

该模块负责将文本内容转换为多种格式的文档（PDF、Word、TXT）。
主要功能包括：
- 将 Markdown 格式的文本转换为 PDF、Word、TXT
- 支持流式响应，避免后端文件存储
- 优化的样式和布局设计
- 支持中文字体和复杂排版

技术实现：
- 使用 reportlab 库进行 PDF 转换
- 使用 python-docx 库进行 Word 转换
- 内存中生成文档，直接返回字节流
- 优化的样式模板和布局

该模块为 FindU 系统提供高效的文档输出功能，支持多种格式和流式下载。
"""

import io
import os
import re
import logging
from typing import Optional, Tuple, Dict, Any
from datetime import datetime
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入 reportlab，如果失败则使用降级方案
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY, TA_RIGHT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.lib.colors import HexColor, black, darkblue, darkgreen, lightgrey, white, grey
    from reportlab.platypus.frames import Frame
    from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
    from reportlab.platypus.tableofcontents import TableOfContents
    import platform
    REPORTLAB_AVAILABLE = True
    logger.info("reportlab 可用")
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("reportlab 不可用，PDF功能将被禁用")

# 尝试导入 python-docx
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
    from docx.oxml.shared import OxmlElement, qn
    DOCX_AVAILABLE = True
    logger.info("python-docx 可用")
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("python-docx 不可用，Word功能将被禁用")

class DocumentFormat(Enum):
    """文档格式枚举"""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"

class DocumentService:
    """文档生成服务"""
    
    def __init__(self):
        """初始化文档服务"""
        self.font_registered = False
        if REPORTLAB_AVAILABLE:
            self._register_chinese_fonts()
    
    def _register_chinese_fonts(self) -> bool:
        """
        注册中文字体到 ReportLab
        
        Returns:
            bool: 字体注册是否成功
        """
        if self.font_registered:
            return True
            
        try:
            system = platform.system()
            font_paths = []
            
            if system == "Windows":
                font_paths = [
                    "C:/Windows/Fonts/simhei.ttf",     # 黑体
                    "C:/Windows/Fonts/simsun.ttc",     # 宋体
                    "C:/Windows/Fonts/msyh.ttc",       # 微软雅黑
                ]
            elif system == "Darwin":  # macOS
                font_paths = [
                    "/Library/Fonts/Arial Unicode MS.ttf",
                    "/System/Library/Fonts/STHeiti Light.ttc",
                    "/System/Library/Fonts/PingFang.ttc",
                ]
            elif system == "Linux":
                font_paths = [
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                    "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
                    "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                ]
            
            # 尝试注册找到的第一个有效字体
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        if 'ChineseFont' not in pdfmetrics.getRegisteredFontNames():
                            if font_path.lower().endswith('.ttc'):
                                pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                                pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=0))
                            else:
                                pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                                pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                            
                            logger.info(f"成功注册中文字体: {font_path}")
                            self.font_registered = True
                            return True
                    except Exception as e:
                        logger.warning(f"注册字体失败 {font_path}: {e}")
                        continue
            
            # 如果没有找到系统字体，使用默认字体
            if not self.font_registered:
                logger.warning("未找到系统中文字体，使用默认字体")
                self.font_registered = True  # 标记为已处理，避免重复尝试
                return False
                
        except Exception as e:
            logger.error(f"字体注册过程出错: {e}")
            return False
        
        return self.font_registered
    
    def _parse_markdown_content(self, content: str) -> list:
        """
        解析 Markdown 内容为结构化数据
        
        Args:
            content: Markdown 格式的文档内容
            
        Returns:
            list: 解析后的内容结构列表
        """
        lines = content.split('\n')
        parsed_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                parsed_content.append({'type': 'spacer', 'content': ''})
                continue
            
            # 标题处理
            if line.startswith('# '):
                parsed_content.append({'type': 'h1', 'content': line[2:].strip()})
            elif line.startswith('## '):
                parsed_content.append({'type': 'h2', 'content': line[3:].strip()})
            elif line.startswith('### '):
                parsed_content.append({'type': 'h3', 'content': line[4:].strip()})
            elif line.startswith('#### '):
                parsed_content.append({'type': 'h4', 'content': line[5:].strip()})
            # 列表处理
            elif line.startswith('- ') or line.startswith('* '):
                parsed_content.append({'type': 'list_item', 'content': line[2:].strip()})
            elif re.match(r'^\d+\.\s', line):
                parsed_content.append({'type': 'numbered_list', 'content': re.sub(r'^\d+\.\s', '', line)})
            # 粗体文本
            elif line.startswith('**') and line.endswith('**'):
                parsed_content.append({'type': 'bold', 'content': line[2:-2].strip()})
            # 普通段落
            else:
                parsed_content.append({'type': 'paragraph', 'content': line})
        
        return parsed_content
    
    def _create_header_footer(self, canvas, doc, title: str):
        """
        创建页眉和页脚

        Args:
            canvas: ReportLab画布对象
            doc: 文档对象
            title: 文档标题
        """
        canvas.saveState()

        # 页眉
        canvas.setFont('ChineseFont' if self.font_registered else 'Helvetica', 9)
        canvas.setFillColor(grey)

        # 页眉线
        canvas.setStrokeColor(lightgrey)
        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, A4[1] - doc.topMargin + 20,
                   A4[0] - doc.rightMargin, A4[1] - doc.topMargin + 20)

        # 页眉文字
        canvas.drawString(doc.leftMargin, A4[1] - doc.topMargin + 30, title)
        canvas.drawRightString(A4[0] - doc.rightMargin, A4[1] - doc.topMargin + 30,
                              f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}")

        # 页脚
        canvas.setFont('ChineseFont' if self.font_registered else 'Helvetica', 8)

        # 页脚线
        canvas.line(doc.leftMargin, doc.bottomMargin - 20,
                   A4[0] - doc.rightMargin, doc.bottomMargin - 20)

        # 页码
        page_num = canvas.getPageNumber()
        canvas.drawCentredString(A4[0] / 2, doc.bottomMargin - 35, f"第 {page_num} 页")

        # 页脚信息
        canvas.drawString(doc.leftMargin, doc.bottomMargin - 35, "FindU AI 需求生成器")
        canvas.drawRightString(A4[0] - doc.rightMargin, doc.bottomMargin - 35, "https://findu.ai")

        canvas.restoreState()

    def generate_pdf(self, content: str, title: str = "FindU 项目需求文档") -> bytes:
        """
        生成 PDF 文档
        
        Args:
            content: 文档内容
            title: 文档标题
            
        Returns:
            bytes: PDF 文档的字节数据
        """
        if not REPORTLAB_AVAILABLE:
            raise RuntimeError("PDF 功能不可用，请安装 reportlab")
        
        # 创建内存缓冲区
        buffer = io.BytesIO()
        
        # 创建 PDF 文档，增加页眉页脚空间
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=100,  # 增加顶部边距为页眉留空间
            bottomMargin=100  # 增加底部边距为页脚留空间
        )
        
        # 获取样式表
        styles = getSampleStyleSheet()
        
        # 确定字体
        font_name = 'ChineseFont' if self.font_registered else 'Helvetica'
        font_name_bold = 'ChineseFont-Bold' if self.font_registered else 'Helvetica-Bold'

        # 定义现代化颜色方案
        primary_color = HexColor('#2563eb')      # 现代蓝色
        secondary_color = HexColor('#059669')    # 现代绿色
        accent_color = HexColor('#dc2626')       # 现代红色
        text_color = HexColor('#1f2937')         # 深灰色文字
        light_bg = HexColor('#f8fafc')           # 浅灰背景

        # 创建自定义样式 - 现代化设计
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontName=font_name_bold,
            fontSize=24,
            spaceAfter=40,
            spaceBefore=30,
            alignment=TA_CENTER,
            textColor=primary_color,
            borderWidth=2,
            borderColor=primary_color,
            borderPadding=15,
            backColor=light_bg
        )
        
        heading1_style = ParagraphStyle(
            'CustomHeading1',
            parent=styles['Heading1'],
            fontName=font_name_bold,
            fontSize=18,
            spaceAfter=25,
            spaceBefore=25,
            textColor=primary_color,
            borderWidth=0,
            borderColor=primary_color,
            borderPadding=8,
            backColor=light_bg,
            leftIndent=10,
            rightIndent=10
        )

        heading2_style = ParagraphStyle(
            'CustomHeading2',
            parent=styles['Heading2'],
            fontName=font_name_bold,
            fontSize=16,
            spaceAfter=18,
            spaceBefore=18,
            textColor=secondary_color,
            leftIndent=5
        )

        heading3_style = ParagraphStyle(
            'CustomHeading3',
            parent=styles['Heading3'],
            fontName=font_name_bold,
            fontSize=14,
            spaceAfter=15,
            spaceBefore=15,
            textColor=text_color
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=12,
            spaceAfter=14,
            leading=18,
            alignment=TA_JUSTIFY,
            textColor=text_color,
            firstLineIndent=20  # 段落首行缩进
        )

        list_style = ParagraphStyle(
            'CustomList',
            parent=normal_style,
            leftIndent=25,
            bulletIndent=15,
            spaceAfter=8,
            firstLineIndent=0  # 列表项不需要首行缩进
        )

        # 添加引用样式
        quote_style = ParagraphStyle(
            'CustomQuote',
            parent=normal_style,
            leftIndent=30,
            rightIndent=30,
            spaceAfter=16,
            spaceBefore=16,
            backColor=light_bg,
            borderWidth=0,
            borderColor=primary_color,
            borderPadding=10,
            fontName=font_name,
            fontSize=11,
            textColor=HexColor('#4b5563')  # 稍浅的文字颜色
        )
        
        # 构建文档内容
        story = []

        # 添加封面标题
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 30))

        # 添加生成信息表格
        info_data = [
            ['生成时间', datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')],
            ['生成工具', 'FindU AI 需求生成器'],
            ['文档版本', 'v1.0']
        ]

        info_table = Table(info_data, colWidths=[3*cm, 6*cm])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), light_bg),
            ('TEXTCOLOR', (0, 0), (-1, -1), text_color),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), font_name),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#e5e7eb')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        story.append(info_table)
        story.append(Spacer(1, 40))
        
        # 解析并添加内容
        parsed_content = self._parse_markdown_content(content)
        
        for item in parsed_content:
            item_type = item['type']
            item_content = item['content']
            
            # HTML转义
            item_content = (item_content
                          .replace('&', '&amp;')
                          .replace('<', '&lt;')
                          .replace('>', '&gt;')
                          .replace('"', '&quot;')
                          .replace("'", '&#x27;'))
            
            if item_type == 'h1':
                story.append(Spacer(1, 10))  # 标题前增加空间
                story.append(Paragraph(item_content, heading1_style))
            elif item_type == 'h2':
                story.append(Spacer(1, 8))
                story.append(Paragraph(item_content, heading2_style))
            elif item_type == 'h3':
                story.append(Spacer(1, 6))
                story.append(Paragraph(item_content, heading3_style))
            elif item_type == 'list_item':
                # 使用更美观的列表符号
                story.append(Paragraph(f"▪ {item_content}", list_style))
            elif item_type == 'numbered_list':
                story.append(Paragraph(f"1. {item_content}", list_style))
            elif item_type == 'bold':
                bold_style = ParagraphStyle('Bold', parent=normal_style, fontName=font_name_bold)
                story.append(Paragraph(item_content, bold_style))
            elif item_type == 'quote':
                # 处理引用块
                story.append(Paragraph(item_content, quote_style))
            elif item_type == 'spacer':
                story.append(Spacer(1, 12))
            else:  # paragraph
                if item_content and item_content.strip():
                    # 检查是否是引用（以 > 开头）
                    if item_content.strip().startswith('>'):
                        quote_content = item_content.strip()[1:].strip()
                        story.append(Paragraph(quote_content, quote_style))
                    else:
                        story.append(Paragraph(item_content, normal_style))
        
        # 生成 PDF，添加页眉页脚
        doc.build(story, onFirstPage=lambda c, d: self._create_header_footer(c, d, title),
                 onLaterPages=lambda c, d: self._create_header_footer(c, d, title))
        
        # 获取字节数据
        buffer.seek(0)
        pdf_data = buffer.getvalue()
        buffer.close()
        
        logger.info(f"成功生成 PDF 文档，大小: {len(pdf_data)} 字节")
        return pdf_data

    def generate_docx(self, content: str, title: str = "FindU 项目需求文档") -> bytes:
        """
        生成 Word 文档

        Args:
            content: 文档内容
            title: 文档标题

        Returns:
            bytes: Word 文档的字节数据
        """
        if not DOCX_AVAILABLE:
            raise RuntimeError("Word 功能不可用，请安装 python-docx")

        # 创建文档
        doc = Document()

        # 设置文档样式
        style = doc.styles['Normal']
        font = style.font
        font.name = 'Microsoft YaHei'
        font.size = Pt(11)

        # 添加标题
        title_paragraph = doc.add_heading(title, 0)
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 解析并添加内容
        parsed_content = self._parse_markdown_content(content)

        for item in parsed_content:
            item_type = item['type']
            item_content = item['content']

            if item_type == 'h1':
                heading = doc.add_heading(item_content, level=1)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif item_type == 'h2':
                heading = doc.add_heading(item_content, level=2)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif item_type == 'h3':
                heading = doc.add_heading(item_content, level=3)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif item_type == 'h4':
                heading = doc.add_heading(item_content, level=4)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
            elif item_type == 'list_item':
                paragraph = doc.add_paragraph(item_content, style='List Bullet')
            elif item_type == 'numbered_list':
                paragraph = doc.add_paragraph(item_content, style='List Number')
            elif item_type == 'bold':
                paragraph = doc.add_paragraph()
                run = paragraph.add_run(item_content)
                run.bold = True
            elif item_type == 'spacer':
                doc.add_paragraph()
            else:  # paragraph
                if item_content:
                    paragraph = doc.add_paragraph(item_content)
                    paragraph.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

        # 保存到内存缓冲区
        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)
        docx_data = buffer.getvalue()
        buffer.close()

        logger.info(f"成功生成 Word 文档，大小: {len(docx_data)} 字节")
        return docx_data

    def generate_txt(self, content: str, title: str = "FindU 项目需求文档") -> bytes:
        """
        生成纯文本文档

        Args:
            content: 文档内容
            title: 文档标题

        Returns:
            bytes: 文本文档的字节数据
        """
        # 构建文本内容
        text_lines = []

        # 添加标题
        text_lines.append("=" * 60)
        text_lines.append(title.center(60))
        text_lines.append("=" * 60)
        text_lines.append("")
        text_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        text_lines.append("")
        text_lines.append("-" * 60)
        text_lines.append("")

        # 解析并添加内容
        parsed_content = self._parse_markdown_content(content)

        for item in parsed_content:
            item_type = item['type']
            item_content = item['content']

            if item_type == 'h1':
                text_lines.append("")
                text_lines.append("=" * len(item_content))
                text_lines.append(item_content)
                text_lines.append("=" * len(item_content))
                text_lines.append("")
            elif item_type == 'h2':
                text_lines.append("")
                text_lines.append(item_content)
                text_lines.append("-" * len(item_content))
                text_lines.append("")
            elif item_type == 'h3':
                text_lines.append("")
                text_lines.append(f"### {item_content}")
                text_lines.append("")
            elif item_type == 'h4':
                text_lines.append("")
                text_lines.append(f"#### {item_content}")
                text_lines.append("")
            elif item_type == 'list_item':
                text_lines.append(f"  • {item_content}")
            elif item_type == 'numbered_list':
                text_lines.append(f"  1. {item_content}")
            elif item_type == 'bold':
                text_lines.append(f"**{item_content}**")
            elif item_type == 'spacer':
                text_lines.append("")
            else:  # paragraph
                if item_content:
                    text_lines.append(item_content)
                    text_lines.append("")

        # 转换为字节数据
        text_content = "\n".join(text_lines)
        txt_data = text_content.encode('utf-8')

        logger.info(f"成功生成文本文档，大小: {len(txt_data)} 字节")
        return txt_data

    def generate_document(self, content: str, format_type: DocumentFormat,
                         title: str = "FindU 项目需求文档") -> Tuple[bytes, str]:
        """
        生成指定格式的文档

        Args:
            content: 文档内容
            format_type: 文档格式
            title: 文档标题

        Returns:
            Tuple[bytes, str]: (文档字节数据, MIME类型)
        """
        try:
            if format_type == DocumentFormat.PDF:
                data = self.generate_pdf(content, title)
                mime_type = "application/pdf"
            elif format_type == DocumentFormat.DOCX:
                data = self.generate_docx(content, title)
                mime_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            elif format_type == DocumentFormat.TXT:
                data = self.generate_txt(content, title)
                mime_type = "text/plain; charset=utf-8"
            else:
                raise ValueError(f"不支持的文档格式: {format_type}")

            return data, mime_type

        except Exception as e:
            logger.error(f"生成 {format_type.value} 文档失败: {e}")
            raise

    def get_filename(self, title: str, format_type: DocumentFormat) -> str:
        """
        生成文件名

        Args:
            title: 文档标题
            format_type: 文档格式

        Returns:
            str: 文件名
        """
        # 清理标题中的特殊字符，只保留ASCII字符
        import unicodedata

        # 将中文转换为拼音或使用默认名称
        if any('\u4e00' <= char <= '\u9fff' for char in title):
            # 包含中文字符，使用默认名称
            clean_title = "FindU-Document"
        else:
            # 清理标题中的特殊字符
            clean_title = re.sub(r'[^\w\s-]', '', title).strip()
            clean_title = re.sub(r'[-\s]+', '-', clean_title)

        # 如果标题为空，使用默认名称
        if not clean_title:
            clean_title = "FindU-Document"

        # 添加时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        return f"{clean_title}_{timestamp}.{format_type.value}"

    def is_format_supported(self, format_type: DocumentFormat) -> bool:
        """
        检查指定格式是否支持

        Args:
            format_type: 文档格式

        Returns:
            bool: 是否支持该格式
        """
        if format_type == DocumentFormat.PDF:
            return REPORTLAB_AVAILABLE
        elif format_type == DocumentFormat.DOCX:
            return DOCX_AVAILABLE
        elif format_type == DocumentFormat.TXT:
            return True  # 文本格式总是支持的
        else:
            return False

    def get_supported_formats(self) -> list:
        """
        获取支持的文档格式列表

        Returns:
            list: 支持的格式列表
        """
        supported = []

        for format_type in DocumentFormat:
            if self.is_format_supported(format_type):
                supported.append(format_type.value)

        return supported

# 创建全局文档服务实例
document_service = DocumentService()
