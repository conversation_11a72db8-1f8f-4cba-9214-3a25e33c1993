"""
文档生成服务模块

该模块负责将文本内容转换为多种格式的文档（PDF、Word、TXT）。
主要功能包括：
- 将 Markdown 格式的文本转换为 PDF、Word、TXT
- 支持流式响应，避免后端文件存储
- 优化的样式和布局设计
- 支持中文字体和复杂排版

技术实现：
- 使用 reportlab 库进行 PDF 转换
- 使用 python-docx 库进行 Word 转换
- 内存中生成文档，直接返回字节流
- 优化的样式模板和布局

该模块为 FindU 系统提供高效的文档输出功能，支持多种格式和流式下载。
"""

import os
import logging
from typing import Optional

# 配置日志
logger = logging.getLogger(__name__)

# 尝试导入 reportlab，如果失败则使用降级方案
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_LEFT, TA_CENTER
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    import platform
    REPORTLAB_AVAILABLE = True
    logger.info("reportlab 可用")
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("reportlab 不可用，将使用文本文件降级方案")

def _register_chinese_fonts():
    """
    注册中文字体到 ReportLab
    
    尝试在系统中找到并注册可用的中文字体，支持跨平台。
    """
    try:
        system = platform.system()
        font_registered = False
        
        # 定义不同系统的字体路径，优先选择.ttf文件而不是.ttc
        font_paths = []
        
        if system == "Windows":
            # Windows 系统字体路径 - 优先使用TTF格式
            font_paths = [
                "C:/Windows/Fonts/simhei.ttf",     # 黑体
                "C:/Windows/Fonts/simsun.ttc",     # 宋体
                "C:/Windows/Fonts/msyh.ttc",       # 微软雅黑
                "C:/Windows/Fonts/STZHONGS.TTF",   # 华文中宋
                "C:/Windows/Fonts/SIMYOU.TTF",     # 幼圆
            ]
        elif system == "Darwin":  # macOS
            font_paths = [
                "/Library/Fonts/Arial Unicode MS.ttf",              # Arial Unicode MS
                "/System/Library/Fonts/STHeiti Light.ttc",          # 华文黑体
                "/System/Library/Fonts/PingFang.ttc",               # 苹方
                "/System/Library/Fonts/Hiragino Sans GB.ttc",       # 冬青黑体
            ]
        elif system == "Linux":
            # Linux 系统字体路径
            font_paths = [
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",   # 文泉驿微米黑
                "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",     # 文泉驿正黑
                "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",  # 思源黑体
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            ]
        
        # 尝试注册找到的第一个有效字体
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    # 检查是否已经注册过
                    if 'ChineseFont' not in pdfmetrics.getRegisteredFontNames():
                        # 对于TTC文件，尝试指定子字体索引
                        if font_path.lower().endswith('.ttc'):
                            # TTC文件可能包含多个字体，尝试索引0
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path, subfontIndex=0))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path, subfontIndex=0))
                        else:
                            pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                            pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_path))
                        
                        # 测试字体是否可以正确渲染中文
                        test_success = _test_font_rendering('ChineseFont')
                        if test_success:
                            logger.info(f"成功注册并验证中文字体: {font_path}")
                            font_registered = True
                            break
                        else:
                            logger.warning(f"字体注册成功但无法正确渲染中文: {font_path}")
                    else:
                        logger.info("中文字体已经注册")
                        font_registered = True
                        break
                        
                except Exception as e:
                    logger.warning(f"注册字体失败 {font_path}: {e}")
                    continue
        
        # 如果没有找到系统字体，尝试使用内置的备用方案
        if not font_registered:
            logger.warning("未找到系统中文字体，尝试使用备用方案")
            font_registered = _use_unicode_fallback()
        
        return font_registered
        
    except Exception as e:
        logger.error(f"字体注册过程出错: {e}")
        return False

def _test_font_rendering(font_name):
    """
    测试字体是否能正确渲染中文字符
    """
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        import tempfile
        import os
        
        # 创建临时PDF测试字体
        temp_file = tempfile.mktemp(suffix='.pdf')
        c = canvas.Canvas(temp_file, pagesize=letter)
        c.setFont(font_name, 12)
        
        # 尝试绘制中文字符
        c.drawString(100, 750, "测试中文字符")
        c.save()
        
        # 检查文件是否成功创建
        success = os.path.exists(temp_file) and os.path.getsize(temp_file) > 0
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
            
        return success
    except:
        return False

def _use_unicode_fallback():
    """
    使用Unicode备用方案，不依赖外部字体文件
    """
    try:
        # 尝试使用ReportLab内置的字体处理Unicode
        from reportlab.lib.fonts import addMapping
        from reportlab.pdfbase.pdfmetrics import registerFontFamily
        
        # 注册字体族，使用Helvetica作为基础
        registerFontFamily('ChineseFont', normal='Helvetica', bold='Helvetica-Bold')
        addMapping('ChineseFont', 0, 0, 'Helvetica')  # normal
        addMapping('ChineseFont', 0, 1, 'Helvetica-Bold')  # bold
        addMapping('ChineseFont', 1, 0, 'Helvetica-Oblique')  # italic
        addMapping('ChineseFont', 1, 1, 'Helvetica-BoldOblique')  # bold+italic
        
        logger.info("使用Unicode备用字体方案")
        return True
    except Exception as e:
        logger.error(f"Unicode备用方案失败: {e}")
        return False

def _download_and_register_fallback_font():
    """
    下载并注册备用的开源中文字体
    """
    try:
        import urllib.request
        import tempfile
        
        # 使用开源的思源黑体作为备用字体
        font_url = "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN-Regular.otf"
        
        # 创建字体缓存目录
        font_cache_dir = os.path.join(tempfile.gettempdir(), "findu_fonts")
        os.makedirs(font_cache_dir, exist_ok=True)
        
        font_file_path = os.path.join(font_cache_dir, "SourceHanSansCN-Regular.otf")
        
        # 如果字体文件不存在，则下载
        if not os.path.exists(font_file_path):
            logger.info("正在下载开源中文字体...")
            urllib.request.urlretrieve(font_url, font_file_path)
            logger.info(f"字体下载完成: {font_file_path}")
        
        # 注册字体
        pdfmetrics.registerFont(TTFont('ChineseFont', font_file_path))
        pdfmetrics.registerFont(TTFont('ChineseFont-Bold', font_file_path))
        logger.info("备用字体注册成功")
        return True
        
    except Exception as e:
        logger.error(f"备用字体下载/注册失败: {e}")
        return False

def _generate_pdf_with_reportlab(content: str, output_file: str) -> bool:
    """
    使用 reportlab 生成 PDF 文件

    Args:
        content (str): 文档内容
        output_file (str): 输出文件路径

    Returns:
        bool: 生成成功返回 True，否则返回 False
    """
    try:
        # 注册中文字体
        font_available = _register_chinese_fonts()
        
        if not font_available:
            logger.warning("中文字体注册失败，将使用基础字体")
        
        # 创建 PDF 文档，设置更详细的参数
        doc = SimpleDocTemplate(
            output_file, 
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )

        # 获取样式表
        styles = getSampleStyleSheet()

        # 确定使用的字体名称，如果中文字体不可用，使用系统默认字体
        if font_available:
            font_name = 'ChineseFont'
            font_name_bold = 'ChineseFont-Bold'
        else:
            # 使用系统默认字体，这些字体通常能更好地处理Unicode
            font_name = 'Times-Roman'
            font_name_bold = 'Times-Bold'

        # 创建自定义样式，明确指定字体编码
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName=font_name_bold,
            fontSize=18,
            spaceAfter=30,
            spaceBefore=20,
            alignment=TA_CENTER,
            textColor='black'
        )

        heading1_style = ParagraphStyle(
            'CustomHeading1',
            parent=styles['Heading1'],
            fontName=font_name_bold,
            fontSize=16,
            spaceAfter=20,
            spaceBefore=20,
            textColor='black'
        )

        heading2_style = ParagraphStyle(
            'CustomHeading2',
            parent=styles['Heading2'],
            fontName=font_name_bold,
            fontSize=14,
            spaceAfter=15,
            spaceBefore=15,
            textColor='black'
        )

        heading3_style = ParagraphStyle(
            'CustomHeading3',
            parent=styles['Heading3'],
            fontName=font_name_bold,
            fontSize=12,
            spaceAfter=12,
            spaceBefore=12,
            textColor='black'
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=12,
            spaceAfter=12,
            leftIndent=0,
            rightIndent=0,
            textColor='black',
            leading=16,  # 行间距
            wordWrap='LTR'  # 文字换行方向
        )

        # 构建文档内容
        story = []

        # 添加标题 - 使用简单的ASCII标题避免字体问题
        if font_available:
            title_text = "FindU 项目需求文档"
        else:
            title_text = "FindU Project Requirements Document"
        
        story.append(Paragraph(title_text, title_style))
        story.append(Spacer(1, 20))

        # 处理内容，按行分割并转换为段落
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line:
                # 对内容进行编码处理，确保特殊字符正确显示
                try:
                    # 首先尝试UTF-8编码
                    if isinstance(line, str):
                        processed_line = line
                    else:
                        processed_line = line.decode('utf-8')
                except:
                    # 如果编码失败，使用ASCII字符替换
                    processed_line = line.encode('ascii', 'ignore').decode('ascii')
                
                # HTML转义
                processed_line = (processed_line
                                .replace('&', '&amp;')
                                .replace('<', '&lt;')
                                .replace('>', '&gt;')
                                .replace('"', '&quot;')
                                .replace("'", '&#x27;'))
                
                # 简单的 Markdown 标题处理
                if processed_line.startswith('# '):
                    story.append(Paragraph(processed_line[2:], heading1_style))
                elif processed_line.startswith('## '):
                    story.append(Paragraph(processed_line[3:], heading2_style))
                elif processed_line.startswith('### '):
                    story.append(Paragraph(processed_line[4:], heading3_style))
                elif processed_line.startswith('- ') or processed_line.startswith('* '):
                    # 处理列表项
                    list_item = f"• {processed_line[2:]}"
                    list_style = ParagraphStyle(
                        'ListItem',
                        parent=normal_style,
                        leftIndent=20,
                        bulletIndent=10,
                        fontName=font_name
                    )
                    story.append(Paragraph(list_item, list_style))
                elif processed_line.startswith('**') and processed_line.endswith('**'):
                    # 处理粗体文本
                    bold_text = processed_line[2:-2]
                    bold_style = ParagraphStyle(
                        'BoldText',
                        parent=normal_style,
                        fontName=font_name_bold
                    )
                    story.append(Paragraph(bold_text, bold_style))
                else:
                    # 处理普通段落
                    story.append(Paragraph(processed_line, normal_style))
            else:
                story.append(Spacer(1, 8))

        # 生成 PDF，增加错误处理
        try:
            doc.build(story)
            logger.info(f"成功使用 reportlab 生成 PDF: {output_file}")
            return True
        except Exception as build_error:
            logger.error(f"PDF构建失败: {build_error}")
            # 尝试使用更简单的文档结构重新生成
            return _generate_simple_pdf(content, output_file)

    except Exception as e:
        logger.error(f"reportlab PDF 生成失败: {e}")
        return False

def _generate_simple_pdf(content: str, output_file: str) -> bool:
    """
    生成简化版本的PDF，避免复杂的字体和样式问题
    """
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 创建简单的PDF画布
        c = canvas.Canvas(output_file, pagesize=A4)
        width, height = A4
        
        # 设置基本字体
        c.setFont("Helvetica-Bold", 16)
        
        # 添加标题
        title = "FindU Project Requirements Document"
        c.drawCentredText(width/2, height-50, title)
        
        # 设置正文字体
        c.setFont("Helvetica", 12)
        
        # 处理内容
        y_position = height - 100
        line_height = 20
        
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and y_position > 50:  # 确保不超出页面底部
                # 简单处理，移除特殊字符
                clean_line = ''.join(char if ord(char) < 128 else '?' for char in line)
                
                # 处理长行，自动换行
                if len(clean_line) > 80:
                    words = clean_line.split(' ')
                    current_line = ""
                    for word in words:
                        if len(current_line + word) < 80:
                            current_line += word + " "
                        else:
                            if current_line:
                                c.drawString(50, y_position, current_line.strip())
                                y_position -= line_height
                            current_line = word + " "
                    if current_line:
                        c.drawString(50, y_position, current_line.strip())
                        y_position -= line_height
                else:
                    c.drawString(50, y_position, clean_line)
                    y_position -= line_height
        
        c.save()
        logger.info(f"成功生成简化版PDF: {output_file}")
        return True
        
    except Exception as e:
        logger.error(f"简化版PDF生成也失败: {e}")
        return False

def _generate_text_file(content: str, locale: str, output_dir: str) -> str:
    """
    生成文本文件作为 PDF 的降级方案

    Args:
        content (str): 文档内容
        locale (str): 语言代码
        output_dir (str): 输出目录

    Returns:
        str: 生成的文件路径
    """
    import time
    timestamp = int(time.time())
    output_file = f"{output_dir}/demand_{locale}_{timestamp}.txt"

    # 添加文件头说明
    header = f"""
=== FindU 项目需求文档 ===
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
语言: {'中文' if locale == 'zh' else 'English'}
格式: 文本格式 (PDF 功能暂不可用)

{'=' * 50}

"""

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(header + content)

    logger.info(f"生成文本文件: {output_file}")
    return output_file

def generate_pdf_document(content: str, locale: str) -> str:
    """
    生成文档文件（PDF 或文本格式）

    将文本内容转换为文档文件并返回可访问的 URL。
    优先尝试生成 PDF，如果系统不支持则降级为文本文件。

    处理流程：
    1. 检查系统是否支持 PDF 生成
    2. 从环境变量获取存储路径配置
    3. 确保输出目录存在
    4. 生成唯一的文件名
    5. 尝试生成 PDF，失败则生成文本文件
    6. 返回可访问的 URL

    Args:
        content (str): 要转换的文档内容，支持 HTML 和 Markdown 格式
        locale (str): 语言代码，用于文件命名

    Returns:
        str: 生成的文档文件的完整访问 URL

    Raises:
        OSError: 当无法创建输出目录时
        IOError: 当文档生成失败时

    Example:
        >>> content = "# 项目需求文档\n\n这是一个示例文档。"
        >>> url = generate_pdf_document(content, "zh")
        >>> print(url)
        "http://localhost:8000/static/demands/demand_zh_1234567890.pdf"

    Note:
        - 如果系统未安装中文字体，会自动尝试下载开源字体
        - 生产环境中建议预安装中文字体以提高性能
        - 文件名使用时间戳确保唯一性
    """
    try:
        # 从环境变量获取存储路径，默认为 static/demands
        output_dir = os.getenv("STORAGE_PATH", "static/demands")

        # 确保输出目录存在，如果不存在则递归创建
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否可以生成 PDF
        can_generate_pdf = REPORTLAB_AVAILABLE

        if can_generate_pdf:
            try:
                # 生成 PDF 文件
                import time
                timestamp = int(time.time())
                output_file = f"{output_dir}/demand_{locale}_{timestamp}.pdf"

                # 使用 reportlab 生成 PDF
                if _generate_pdf_with_reportlab(content, output_file):
                    logger.info(f"成功生成 PDF 文件: {output_file}")
                else:
                    logger.warning("reportlab PDF 生成失败, 降级为文本文件")
                    output_file = _generate_text_file(content, locale, output_dir)

            except Exception as pdf_error:
                logger.warning(f"PDF 生成失败: {str(pdf_error)}, 降级为文本文件")
                output_file = _generate_text_file(content, locale, output_dir)
        else:
            logger.info("PDF 功能不可用，生成文本文件")
            output_file = _generate_text_file(content, locale, output_dir)

        # 构造完整的访问 URL
        base_url = os.getenv('STORAGE_URL', 'http://localhost:8000')
        return f"{base_url}/{output_file}"

    except Exception as e:
        logger.error(f"文档生成失败: {str(e)}")
        raise IOError(f"Failed to generate document: {str(e)}")