"""
JWT 认证工具模块

该模块提供 JSON Web Token (JWT) 认证相关的工具函数。
主要功能包括：
- JWT 密钥初始化和管理
- Token 验证和解析
- OAuth2 认证方案配置

技术实现：
- 使用 python-jose 库进行 JWT 操作
- 支持 HS256 算法进行 token 签名和验证
- 集成 FastAPI 的 OAuth2 认证框架

安全考虑：
- JWT 密钥应使用强随机字符串
- 生产环境中密钥应通过环境变量安全管理
- Token 验证失败时返回 None，由上层处理认证错误

该模块为 FindU 系统提供用户认证基础设施，虽然当前 MVP 版本暂未启用，
但为未来的用户管理功能预留了完整的认证接口。
"""

from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
import os

# OAuth2 认证方案配置
# 定义 token 获取的 URL 端点，用于 FastAPI 自动生成的认证文档
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 全局 JWT 密钥变量
# 在应用启动时通过 init_jwt 函数初始化
JWT_SECRET = None

def init_jwt(secret: str):
    """
    初始化 JWT 认证系统

    设置用于签名和验证 JWT token 的密钥。
    该函数应在应用启动时调用，通常在 main.py 中执行。

    Args:
        secret (str): JWT 签名密钥，应为强随机字符串

    Note:
        - 密钥一旦设置，不应在运行时更改
        - 生产环境中应使用至少 256 位的随机密钥
        - 密钥泄露会导致安全风险，需妥善保管

    Example:
        >>> init_jwt("your_super_secret_jwt_key_here")
    """
    global JWT_SECRET
    JWT_SECRET = secret

def verify_token(token: str):
    """
    验证 JWT token 的有效性

    解析并验证 JWT token，返回其中包含的用户信息。
    如果 token 无效、过期或格式错误，返回 None。

    Args:
        token (str): 要验证的 JWT token 字符串

    Returns:
        dict | None: 如果验证成功，返回 token 中的 payload 字典；
                    如果验证失败，返回 None

    Raises:
        无异常抛出，所有错误都会被捕获并返回 None

    Example:
        >>> token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
        >>> payload = verify_token(token)
        >>> if payload:
        ...     user_id = payload.get("user_id")
        ...     print(f"认证成功，用户ID: {user_id}")
        ... else:
        ...     print("Token 验证失败")

    Note:
        - 使用 HS256 算法进行签名验证
        - 自动检查 token 的过期时间
        - 验证失败的原因可能包括：签名错误、token 过期、格式无效等
    """
    try:
        # 使用配置的密钥和 HS256 算法解码 JWT token
        payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return payload
    except JWTError:
        # 捕获所有 JWT 相关错误，包括签名验证失败、token 过期等
        # 返回 None 表示验证失败，由调用方处理认证错误
        return None