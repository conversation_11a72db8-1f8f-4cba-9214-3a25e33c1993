#!/usr/bin/env python3
"""
文档生成功能测试脚本

该脚本用于测试完整的文档生成流程，包括：
1. 案例生成
2. 文档生成
3. 文件访问验证

使用方法：
python test_document_generation.py
"""

import asyncio
import json
import os
import sys
import requests
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_case_generation():
    """测试案例生成功能"""
    print("🧪 测试案例生成...")
    
    url = "http://localhost:8000/api/generate-cases"
    data = {
        "prompt": "我想要一个在线教育平台",
        "locale": "zh"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        cases = result.get("cases", [])
        
        print(f"✅ 案例生成成功，共生成 {len(cases)} 个案例")
        
        if cases:
            first_case = cases[0]
            print(f"   第一个案例: {first_case.get('title', 'N/A')}")
            return first_case
        else:
            print("❌ 没有生成任何案例")
            return None
            
    except Exception as e:
        print(f"❌ 案例生成失败: {str(e)}")
        return None

async def test_document_generation(case_data):
    """测试文档生成功能"""
    print("\n🧪 测试文档生成...")
    
    if not case_data:
        print("❌ 没有案例数据，跳过文档生成测试")
        return None
    
    url = "http://localhost:8000/api/generate-demand"
    data = {
        "case_id": case_data.get("id", 0),
        "case_title": case_data.get("title", "测试案例"),
        "case_description": case_data.get("description", "测试描述"),
        "case_details": case_data.get("details", ["功能1", "功能2"]),
        "locale": "zh"
    }
    
    try:
        response = requests.post(url, json=data, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        document_url = result.get("documentUrl")
        
        if document_url:
            print(f"✅ 文档生成成功")
            print(f"   文档 URL: {document_url}")
            return document_url
        else:
            print("❌ 文档生成失败：没有返回文档 URL")
            return None
            
    except Exception as e:
        print(f"❌ 文档生成失败: {str(e)}")
        return None

def test_file_access(document_url):
    """测试文件访问"""
    print("\n🧪 测试文件访问...")
    
    if not document_url:
        print("❌ 没有文档 URL，跳过文件访问测试")
        return False
    
    try:
        response = requests.get(document_url, timeout=10)
        response.raise_for_status()
        
        content_length = len(response.content)
        content_type = response.headers.get('content-type', 'unknown')
        
        print(f"✅ 文件访问成功")
        print(f"   文件大小: {content_length} 字节")
        print(f"   内容类型: {content_type}")
        
        # 如果是文本文件，显示前几行内容
        if 'text' in content_type.lower():
            content_preview = response.text[:200]
            print(f"   内容预览: {content_preview}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件访问失败: {str(e)}")
        return False

def test_health_check():
    """测试后端健康状态"""
    print("🧪 测试后端健康状态...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        response.raise_for_status()
        
        result = response.json()
        status = result.get("status")
        
        if status == "healthy":
            print("✅ 后端服务正常运行")
            return True
        else:
            print(f"❌ 后端服务状态异常: {status}")
            return False
            
    except Exception as e:
        print(f"❌ 后端服务连接失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试 FindU 文档生成功能\n")
    
    # 1. 健康检查
    if not test_health_check():
        print("\n❌ 后端服务不可用，测试终止")
        return
    
    # 2. 测试案例生成
    case_data = await test_case_generation()
    
    # 3. 测试文档生成
    document_url = await test_document_generation(case_data)
    
    # 4. 测试文件访问
    file_accessible = test_file_access(document_url)
    
    # 5. 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"   后端服务: ✅ 正常")
    print(f"   案例生成: {'✅ 成功' if case_data else '❌ 失败'}")
    print(f"   文档生成: {'✅ 成功' if document_url else '❌ 失败'}")
    print(f"   文件访问: {'✅ 成功' if file_accessible else '❌ 失败'}")
    
    if case_data and document_url and file_accessible:
        print("\n🎉 所有测试通过！文档生成功能正常工作。")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    asyncio.run(main())
