#!/bin/bash

# FindU 文档生成功能测试脚本
# 该脚本测试完整的前后端通信流程

echo "🚀 开始测试 FindU 文档生成功能"
echo ""

# 1. 健康检查
echo "🧪 测试后端健康状态..."
health_response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json http://localhost:8000/health)
health_code="${health_response: -3}"

if [ "$health_code" = "200" ]; then
    echo "✅ 后端服务正常运行"
else
    echo "❌ 后端服务不可用 (HTTP $health_code)"
    exit 1
fi

# 2. 测试案例生成
echo ""
echo "🧪 测试案例生成..."
cases_response=$(curl -s -w "%{http_code}" -o /tmp/cases_response.json \
    -X POST http://localhost:8000/api/generate-cases \
    -H "Content-Type: application/json" \
    -d '{"prompt": "我想要一个在线教育平台", "locale": "zh"}')

cases_code="${cases_response: -3}"

if [ "$cases_code" = "200" ]; then
    echo "✅ 案例生成成功"
    
    # 提取第一个案例的信息
    case_title=$(cat /tmp/cases_response.json | python3 -c "
import json, sys
data = json.load(sys.stdin)
cases = data.get('cases', [])
if cases:
    print(cases[0].get('title', 'N/A'))
else:
    print('N/A')
")
    
    case_description=$(cat /tmp/cases_response.json | python3 -c "
import json, sys
data = json.load(sys.stdin)
cases = data.get('cases', [])
if cases:
    print(cases[0].get('description', 'N/A'))
else:
    print('N/A')
")
    
    case_details=$(cat /tmp/cases_response.json | python3 -c "
import json, sys
data = json.load(sys.stdin)
cases = data.get('cases', [])
if cases:
    details = cases[0].get('details', [])
    print(json.dumps(details))
else:
    print('[]')
")
    
    echo "   第一个案例: $case_title"
    
else
    echo "❌ 案例生成失败 (HTTP $cases_code)"
    cat /tmp/cases_response.json
    exit 1
fi

# 3. 测试文档生成
echo ""
echo "🧪 测试文档生成..."

# 构建文档生成请求
doc_request=$(python3 -c "
import json
data = {
    'case_id': 0,
    'case_title': '$case_title',
    'case_description': '$case_description',
    'case_details': $case_details,
    'locale': 'zh'
}
print(json.dumps(data))
")

doc_response=$(curl -s -w "%{http_code}" -o /tmp/doc_response.json \
    -X POST http://localhost:8000/api/generate-demand \
    -H "Content-Type: application/json" \
    -d "$doc_request")

doc_code="${doc_response: -3}"

if [ "$doc_code" = "200" ]; then
    echo "✅ 文档生成成功"
    
    # 提取文档 URL
    document_url=$(cat /tmp/doc_response.json | python3 -c "
import json, sys
data = json.load(sys.stdin)
print(data.get('documentUrl', ''))
")
    
    echo "   文档 URL: $document_url"
    
else
    echo "❌ 文档生成失败 (HTTP $doc_code)"
    cat /tmp/doc_response.json
    exit 1
fi

# 4. 测试文件访问
echo ""
echo "🧪 测试文件访问..."

if [ -n "$document_url" ]; then
    file_response=$(curl -s -w "%{http_code}" -o /tmp/document_file.txt "$document_url")
    file_code="${file_response: -3}"
    
    if [ "$file_code" = "200" ]; then
        echo "✅ 文件访问成功"
        
        file_size=$(wc -c < /tmp/document_file.txt)
        echo "   文件大小: $file_size 字节"
        
        echo "   内容预览:"
        head -n 10 /tmp/document_file.txt | sed 's/^/      /'
        
    else
        echo "❌ 文件访问失败 (HTTP $file_code)"
        exit 1
    fi
else
    echo "❌ 没有文档 URL"
    exit 1
fi

# 5. 总结
echo ""
echo "=================================================="
echo "📊 测试结果总结:"
echo "   后端服务: ✅ 正常"
echo "   案例生成: ✅ 成功"
echo "   文档生成: ✅ 成功"
echo "   文件访问: ✅ 成功"
echo ""
echo "🎉 所有测试通过！文档生成功能正常工作。"

# 清理临时文件
rm -f /tmp/health_response.json /tmp/cases_response.json /tmp/doc_response.json /tmp/document_file.txt
