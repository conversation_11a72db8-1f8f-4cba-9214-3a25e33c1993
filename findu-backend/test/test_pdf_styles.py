#!/usr/bin/env python3
"""
PDF样式测试脚本

测试改进后的PDF生成样式和布局
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.document_service import DocumentService, DocumentFormat

def test_pdf_styles():
    """测试PDF样式生成"""
    print("=== PDF样式测试 ===")
    
    # 创建文档服务
    doc_service = DocumentService()
    
    # 测试内容 - 包含各种格式元素
    test_content = """
# 项目概述

这是一个**现代化的电子商务平台**项目，旨在为用户提供优质的在线购物体验。

## 核心功能

### 用户管理
- 用户注册与登录
- 个人信息管理
- 密码安全机制

### 商品管理
- 商品展示与搜索
- 分类浏览功能
- 商品详情页面

> 重要提示：所有商品信息需要经过严格审核，确保信息的准确性和合规性。

## 技术架构

### 前端技术
- React.js 18
- TypeScript
- Tailwind CSS

### 后端技术
- Node.js
- Express.js
- MongoDB

## 项目时间线

1. **需求分析阶段** (1-2周)
   - 收集业务需求
   - 制定技术方案
   - 设计系统架构

2. **开发阶段** (8-10周)
   - 前端界面开发
   - 后端API开发
   - 数据库设计

3. **测试阶段** (2-3周)
   - 单元测试
   - 集成测试
   - 用户验收测试

## 预期成果

通过本项目的实施，我们期望达到以下目标：

- 提供流畅的用户体验
- 实现高性能的系统架构
- 确保数据安全和隐私保护

> 注意：项目开发过程中需要严格遵循代码规范和最佳实践。

## 总结

本项目将采用现代化的技术栈，构建一个功能完善、性能优异的电子商务平台。
"""
    
    try:
        print("正在生成PDF文档...")
        
        # 生成PDF
        pdf_data = doc_service.generate_pdf(test_content, "FindU 项目需求文档 - 样式测试版")
        
        # 保存到文件
        output_file = "test_styled_document.pdf"
        with open(output_file, 'wb') as f:
            f.write(pdf_data)
        
        print(f"✅ PDF生成成功!")
        print(f"   文件大小: {len(pdf_data)} 字节")
        print(f"   保存位置: {output_file}")
        print(f"   样式特性:")
        print(f"     - 现代化颜色方案")
        print(f"     - 页眉页脚设计")
        print(f"     - 改进的标题样式")
        print(f"     - 优化的列表和引用")
        print(f"     - 信息表格展示")
        
    except Exception as e:
        print(f"❌ PDF生成失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_format_support():
    """测试格式支持"""
    print("\n=== 格式支持测试 ===")
    
    doc_service = DocumentService()
    
    formats = [DocumentFormat.PDF, DocumentFormat.DOCX, DocumentFormat.TXT]
    
    for fmt in formats:
        supported = doc_service.is_format_supported(fmt)
        status = "✅ 支持" if supported else "❌ 不支持"
        print(f"  {fmt.value.upper()}: {status}")

if __name__ == "__main__":
    test_format_support()
    test_pdf_styles()
