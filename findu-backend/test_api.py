#!/usr/bin/env python3
"""
简单的API测试脚本
用于验证FindU后端API的基本功能
"""

import requests
import json
import sys

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API健康检查通过")
            return True
        else:
            print(f"❌ API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_generate_cases():
    """测试案例生成功能"""
    try:
        payload = {
            "prompt": "我想要一个简单的博客系统",
            "locale": "zh"
        }
        
        response = requests.post(
            "http://localhost:8000/api/generate-cases",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            # 检查返回的数据结构
            if "cases" in data and isinstance(data["cases"], list) and len(data["cases"]) > 0:
                cases = data["cases"]
                print("✅ 案例生成功能正常")
                print(f"   生成了 {len(cases)} 个案例")
                for i, case in enumerate(cases[:2]):  # 只显示前2个案例
                    print(f"   案例 {i+1}: {case.get('title', 'N/A')}")
                return True
            else:
                print("❌ 案例生成返回数据格式错误")
                print(f"   实际返回: {data}")
                return False
        else:
            print(f"❌ 案例生成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 案例生成测试失败: {e}")
        return False

def test_generate_document():
    """测试文档生成功能"""
    try:
        payload = {
            "case_id": 0,
            "case_title": "博客系统",
            "case_description": "一个简单的博客管理系统",
            "case_details": ["用户注册登录", "文章发布", "评论功能"],
            "locale": "zh",
            "format": "pdf"
        }
        
        response = requests.post(
            "http://localhost:8000/api/generate-demand",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            # 检查返回的数据结构
            if "documentData" in data and "filename" in data:
                print("✅ 文档生成功能正常")
                print(f"   生成文档: {data.get('filename', 'N/A')}")
                print(f"   文档大小: {data.get('size', 'N/A')} 字节")
                return True
            else:
                print("❌ 文档生成返回数据格式错误")
                print(f"   实际返回: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                return False
        else:
            print(f"❌ 文档生成失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文档生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试FindU API功能...")
    print("=" * 50)
    
    tests = [
        ("API健康检查", test_api_health),
        ("案例生成功能", test_generate_cases),
        ("文档生成功能", test_generate_document),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
