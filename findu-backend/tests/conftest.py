"""
Pytest configuration and fixtures for FindU backend tests
"""

import pytest
import asyncio
import os
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Add the app directory to the Python path
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from main import app
from app.services.ai_service import AIService, AIConfig
from app.services.document_service import DocumentService
from app.db.mongodb import MongoDB


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
async def async_client():
    """Create an async test client for the FastAPI app."""
    async with Async<PERSON>lient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_ai_config():
    """Create a mock AI configuration."""
    return AIConfig(
        api_key="test_api_key",
        base_url="https://api.test.com/v1",
        model="test-model",
        provider="test",
        timeout=30.0,
        max_retries=3,
        temperature=0.7,
        max_tokens=3000
    )


@pytest.fixture
def mock_ai_service(mock_ai_config):
    """Create a mock AI service."""
    service = AIService(mock_ai_config)
    service._make_chat_completion = AsyncMock()
    service._is_api_available = Mock(return_value=True)
    return service


@pytest.fixture
def mock_document_service():
    """Create a mock document service."""
    service = DocumentService()
    return service


@pytest.fixture
def mock_mongodb():
    """Create a mock MongoDB instance."""
    mock_db = Mock(spec=MongoDB)
    mock_db.save_prompt = Mock(return_value="test_prompt_id")
    mock_db.save_case = Mock(return_value="test_case_id")
    mock_db.save_document_record = Mock(return_value="test_doc_id")
    mock_db.close = Mock()
    return mock_db


@pytest.fixture
def temp_static_dir():
    """Create a temporary directory for static files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        static_dir = Path(temp_dir) / "static" / "demands"
        static_dir.mkdir(parents=True, exist_ok=True)
        
        # Set environment variable for tests
        original_path = os.environ.get("STATIC_FILES_PATH")
        os.environ["STATIC_FILES_PATH"] = str(static_dir)
        
        yield static_dir
        
        # Restore original environment variable
        if original_path:
            os.environ["STATIC_FILES_PATH"] = original_path
        else:
            os.environ.pop("STATIC_FILES_PATH", None)


@pytest.fixture
def sample_cases():
    """Sample case data for testing."""
    return [
        {
            "id": 0,
            "title": "E-commerce Website",
            "description": "A comprehensive online shopping platform",
            "details": [
                "User registration and authentication",
                "Product catalog management",
                "Shopping cart functionality",
                "Payment processing integration",
                "Order management system"
            ]
        },
        {
            "id": 1,
            "title": "Blog Platform",
            "description": "A content management system for blogging",
            "details": [
                "Article creation and editing",
                "User comments system",
                "SEO optimization",
                "Social media integration",
                "Analytics dashboard"
            ]
        }
    ]


@pytest.fixture
def sample_document_content():
    """Sample document content for testing."""
    return """
# 项目需求文档

## 项目概述

本项目旨在开发一个现代化的电子商务网站。

## 功能需求

### 1. 用户管理
- 用户注册和登录
- 个人资料管理
- 密码重置功能

### 2. 商品管理
- 商品展示
- 商品搜索和筛选
- 商品详情页面

### 3. 购物车功能
- 添加商品到购物车
- 修改商品数量
- 删除商品

### 4. 订单管理
- 订单创建
- 订单状态跟踪
- 订单历史查看

## 技术要求

- 前端：React + TypeScript
- 后端：Node.js + Express
- 数据库：MongoDB
- 部署：Docker + AWS

## 项目时间线

- 第一阶段：基础功能开发（4周）
- 第二阶段：高级功能开发（3周）
- 第三阶段：测试和优化（2周）
- 第四阶段：部署和上线（1周）
"""


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment variables."""
    # Set test environment variables
    test_env = {
        "ENVIRONMENT": "test",
        "JWT_SECRET": "test_jwt_secret",
        "MONGODB_URL": "mongodb://localhost:27017/test_db",
        "X": "test_api_key",
        "FRONTEND_URL": "http://localhost:3000"
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original values
    for key, original_value in original_env.items():
        if original_value is not None:
            os.environ[key] = original_value
        else:
            os.environ.pop(key, None)


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response."""
    return {
        "id": "chatcmpl-test",
        "object": "chat.completion",
        "created": 1234567890,
        "model": "test-model",
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "Mock AI response content"
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 100,
            "completion_tokens": 200,
            "total_tokens": 300
        }
    }


# Pytest markers for test categorization
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.e2e = pytest.mark.e2e
pytest.mark.slow = pytest.mark.slow
pytest.mark.api = pytest.mark.api
pytest.mark.db = pytest.mark.db
pytest.mark.ai = pytest.mark.ai
