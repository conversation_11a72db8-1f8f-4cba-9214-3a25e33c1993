"""
Tests for the AI service module
"""

import pytest
from unittest.mock import Mock, Async<PERSON>ock, patch
import json

from app.services.ai_service import (
    AIService, AIConfig, CaseData, AIServiceError, 
    AIAPIError, AIResponseError, get_ai_service
)


@pytest.mark.unit
@pytest.mark.ai
class TestAIConfig:
    """Test cases for AIConfig class"""

    def test_ai_config_creation(self):
        """Test basic AI config creation"""
        config = AIConfig(
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            provider="test"
        )
        
        assert config.api_key == "test_key"
        assert config.base_url == "https://api.test.com/v1"
        assert config.model == "test-model"
        assert config.provider == "test"
        assert config.timeout == 30.0  # default value
        assert config.max_retries == 3  # default value

    def test_ai_config_from_provider_openai(self):
        """Test creating config from OpenAI provider"""
        config = AIConfig.from_provider("openai", "test_key")
        
        assert config.provider == "openai"
        assert config.api_key == "test_key"
        assert config.base_url == "https://api.openai.com/v1"
        assert config.model == "gpt-3.5-turbo"

    def test_ai_config_from_provider_qwen(self):
        """Test creating config from Qwen provider"""
        config = AIConfig.from_provider("qwen", "test_key", model="qwen-turbo")
        
        assert config.provider == "qwen"
        assert config.api_key == "test_key"
        assert config.base_url == "https://dashscope.aliyuncs.com/compatible-mode/v1"
        assert config.model == "qwen-turbo"

    def test_ai_config_from_provider_invalid(self):
        """Test creating config from invalid provider"""
        with pytest.raises(ValueError, match="不支持的服务提供商"):
            AIConfig.from_provider("invalid_provider", "test_key")

    def test_ai_config_custom_parameters(self):
        """Test AI config with custom parameters"""
        config = AIConfig(
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            provider="test",
            timeout=60.0,
            max_retries=5,
            temperature=0.9,
            max_tokens=4000
        )
        
        assert config.timeout == 60.0
        assert config.max_retries == 5
        assert config.temperature == 0.9
        assert config.max_tokens == 4000


@pytest.mark.unit
@pytest.mark.ai
class TestCaseData:
    """Test cases for CaseData class"""

    def test_case_data_creation(self):
        """Test basic case data creation"""
        case = CaseData(
            id=0,
            title="Test Project",
            description="A test project",
            details=["Feature 1", "Feature 2"]
        )
        
        assert case.id == 0
        assert case.title == "Test Project"
        assert case.description == "A test project"
        assert case.details == ["Feature 1", "Feature 2"]


@pytest.mark.unit
@pytest.mark.ai
class TestAIService:
    """Test cases for AIService class"""

    def test_ai_service_initialization(self, mock_ai_config):
        """Test AI service initialization"""
        service = AIService(mock_ai_config)
        
        assert service.config == mock_ai_config
        assert service.client is not None

    def test_is_api_available_with_key(self, mock_ai_config):
        """Test API availability check with valid key"""
        service = AIService(mock_ai_config)
        
        assert service._is_api_available() is True

    def test_is_api_available_without_key(self):
        """Test API availability check without key"""
        config = AIConfig(
            api_key="",
            base_url="https://api.test.com/v1",
            model="test-model"
        )
        service = AIService(config)
        
        assert service._is_api_available() is False

    @pytest.mark.asyncio
    async def test_make_chat_completion_success(self, mock_ai_service, mock_openai_response):
        """Test successful chat completion"""
        # Mock the OpenAI client
        mock_ai_service.client.chat.completions.create = AsyncMock(
            return_value=Mock(choices=[Mock(message=Mock(content="Test response"))])
        )
        
        messages = [{"role": "user", "content": "Test prompt"}]
        result = await mock_ai_service._make_chat_completion(messages)
        
        assert result == "Test response"

    @pytest.mark.asyncio
    async def test_make_chat_completion_api_error(self, mock_ai_service):
        """Test chat completion with API error"""
        from openai import APIError
        
        mock_ai_service.client.chat.completions.create = AsyncMock(
            side_effect=APIError("API Error", response=Mock(status_code=401), body=None)
        )
        
        messages = [{"role": "user", "content": "Test prompt"}]
        
        with pytest.raises(AIAPIError) as exc_info:
            await mock_ai_service._make_chat_completion(messages)
        
        assert exc_info.value.status_code == 401
        assert exc_info.value.provider == "test"

    @pytest.mark.asyncio
    async def test_make_chat_completion_timeout(self, mock_ai_service):
        """Test chat completion with timeout"""
        import asyncio
        
        mock_ai_service.client.chat.completions.create = AsyncMock(
            side_effect=asyncio.TimeoutError()
        )
        
        messages = [{"role": "user", "content": "Test prompt"}]
        
        with pytest.raises(AIAPIError) as exc_info:
            await mock_ai_service._make_chat_completion(messages)
        
        assert "超时" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_cases_success(self, mock_ai_service):
        """Test successful case generation"""
        # Mock the AI response
        mock_response = json.dumps([
            {
                "id": 0,
                "title": "E-commerce Website",
                "description": "Online shopping platform",
                "details": ["User registration", "Product catalog"]
            }
        ])
        
        mock_ai_service._make_chat_completion = AsyncMock(return_value=mock_response)
        
        result = await mock_ai_service.generate_cases("Build an e-commerce website", "en")
        
        assert len(result) == 1
        assert result[0]["title"] == "E-commerce Website"
        assert result[0]["description"] == "Online shopping platform"

    @pytest.mark.asyncio
    async def test_generate_cases_invalid_json(self, mock_ai_service):
        """Test case generation with invalid JSON response"""
        mock_ai_service._make_chat_completion = AsyncMock(return_value="Invalid JSON")
        
        with pytest.raises(AIResponseError) as exc_info:
            await mock_ai_service.generate_cases("Test prompt", "en")
        
        assert "JSON格式错误" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_cases_empty_prompt(self, mock_ai_service):
        """Test case generation with empty prompt"""
        with pytest.raises(AIServiceError) as exc_info:
            await mock_ai_service.generate_cases("", "en")
        
        assert "提示词不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_cases_api_unavailable(self, mock_ai_config):
        """Test case generation when API is unavailable"""
        # Create service with empty API key
        config = AIConfig(
            api_key="",
            base_url="https://api.test.com/v1",
            model="test-model"
        )
        service = AIService(config)
        
        with patch('app.services.ai_service._generate_mock_cases') as mock_generate:
            mock_generate.return_value = [{"id": 0, "title": "Mock Case"}]
            
            result = await service.generate_cases("Test prompt", "en")
            
            assert len(result) == 1
            assert result[0]["title"] == "Mock Case"
            mock_generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_document_success(self, mock_ai_service, sample_document_content):
        """Test successful document generation"""
        mock_ai_service._make_chat_completion = AsyncMock(return_value=sample_document_content)
        
        case = CaseData(
            id=0,
            title="E-commerce Website",
            description="Online shopping platform",
            details=["User registration", "Product catalog"]
        )
        
        result = await mock_ai_service.generate_document(case, "en")
        
        assert "项目需求文档" in result
        assert len(result) > 500  # Should be substantial content

    @pytest.mark.asyncio
    async def test_generate_document_empty_case(self, mock_ai_service):
        """Test document generation with empty case"""
        with pytest.raises(AIServiceError) as exc_info:
            await mock_ai_service.generate_document(None, "en")
        
        assert "案例数据不能为空" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_document_short_content(self, mock_ai_service):
        """Test document generation with too short content"""
        mock_ai_service._make_chat_completion = AsyncMock(return_value="Too short")
        
        case = CaseData(
            id=0,
            title="Test",
            description="Test description",
            details=["Feature 1"]
        )
        
        with pytest.raises(AIResponseError) as exc_info:
            await mock_ai_service.generate_document(case, "en")
        
        assert "文档内容过短" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_document_missing_sections(self, mock_ai_service):
        """Test document generation with missing required sections"""
        # Content that's long enough but missing required sections
        mock_content = "This is a long document " * 50 + " but missing required sections."
        mock_ai_service._make_chat_completion = AsyncMock(return_value=mock_content)
        
        case = CaseData(
            id=0,
            title="Test",
            description="Test description",
            details=["Feature 1"]
        )
        
        with pytest.raises(AIResponseError) as exc_info:
            await mock_ai_service.generate_document(case, "en")
        
        assert "缺少必要章节" in str(exc_info.value)

    def test_get_ai_service_singleton(self):
        """Test that get_ai_service returns singleton instance"""
        with patch.dict('os.environ', {'X': 'test_key'}):
            service1 = get_ai_service()
            service2 = get_ai_service()
            
            assert service1 is service2

    def test_get_ai_service_no_api_key(self):
        """Test get_ai_service without API key"""
        with patch.dict('os.environ', {}, clear=True):
            service = get_ai_service()
            
            assert service.config.api_key == ""
            assert not service._is_api_available()

    @pytest.mark.asyncio
    async def test_build_system_prompt_cases(self, mock_ai_service):
        """Test building system prompt for cases"""
        from app.services.ai_service import ServiceType
        
        prompt = mock_ai_service._build_system_prompt(ServiceType.CASES, "en")
        
        assert "generate" in prompt.lower()
        assert "cases" in prompt.lower() or "case" in prompt.lower()

    @pytest.mark.asyncio
    async def test_build_system_prompt_document(self, mock_ai_service):
        """Test building system prompt for document"""
        from app.services.ai_service import ServiceType
        
        prompt = mock_ai_service._build_system_prompt(ServiceType.DOCUMENT, "zh")
        
        assert "需求文档" in prompt or "document" in prompt.lower()

    def test_validate_cases_response_valid(self, mock_ai_service):
        """Test validation of valid cases response"""
        valid_cases = [
            {
                "id": 0,
                "title": "Test Project",
                "description": "Test description",
                "details": ["Feature 1", "Feature 2"]
            }
        ]
        
        # Should not raise any exception
        mock_ai_service._validate_cases_response(valid_cases)

    def test_validate_cases_response_invalid_structure(self, mock_ai_service):
        """Test validation of invalid cases response structure"""
        invalid_cases = [
            {
                "id": 0,
                "title": "Test Project",
                # Missing description and details
            }
        ]
        
        with pytest.raises(AIResponseError):
            mock_ai_service._validate_cases_response(invalid_cases)

    def test_validate_cases_response_empty_list(self, mock_ai_service):
        """Test validation of empty cases response"""
        with pytest.raises(AIResponseError):
            mock_ai_service._validate_cases_response([])

    def test_validate_cases_response_not_list(self, mock_ai_service):
        """Test validation of non-list cases response"""
        with pytest.raises(AIResponseError):
            mock_ai_service._validate_cases_response({"not": "a list"})
