"""
Tests for the documents API endpoints
"""

import pytest
from unittest.mock import patch, AsyncMock, Mock
from fastapi.testclient import <PERSON><PERSON>lient
from httpx import AsyncClient

from app.services.ai_service import AIServiceError, CaseData
from app.services.document_service import DocumentFormat


@pytest.mark.api
class TestDocumentsAPI:
    """Test cases for the /api/generate-demand endpoint"""

    def test_generate_demand_success_pdf(self, client: TestClient, sample_document_content):
        """Test successful document generation in PDF format"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            # Mock AI service
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            # Mock document service
            mock_doc_service.save_document.return_value = "test-document.pdf"

            # Make the request
            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "E-commerce Website",
                    "case_description": "Online shopping platform",
                    "case_details": ["User registration", "Product catalog"],
                    "locale": "en",
                    "format": "pdf"
                }
            )

            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert "document_url" in data
            assert "test-document.pdf" in data["document_url"]
            assert "content" in data

            # Verify services were called correctly
            mock_ai_service.generate_document.assert_called_once()
            mock_doc_service.save_document.assert_called_once()

    def test_generate_demand_success_docx(self, client: TestClient, sample_document_content):
        """Test successful document generation in DOCX format"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "test-document.docx"

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Blog Platform",
                    "case_description": "Content management system",
                    "case_details": ["Article creation", "User comments"],
                    "locale": "zh",
                    "format": "docx"
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert "document_url" in data
            assert "test-document.docx" in data["document_url"]

    def test_generate_demand_success_txt(self, client: TestClient, sample_document_content):
        """Test successful document generation in TXT format"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "test-document.txt"

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Mobile App",
                    "case_description": "iOS and Android app",
                    "case_details": ["User interface", "Backend API"],
                    "locale": "en",
                    "format": "txt"
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert "document_url" in data
            assert "test-document.txt" in data["document_url"]

    def test_generate_demand_default_format(self, client: TestClient, sample_document_content):
        """Test document generation with default format (PDF)"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "test-document.pdf"

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Test Project",
                    "case_description": "Test description",
                    "case_details": ["Feature 1", "Feature 2"],
                    "locale": "en"
                    # No format specified, should default to PDF
                }
            )

            assert response.status_code == 200
            # Verify PDF format was used
            args, kwargs = mock_doc_service.save_document.call_args
            assert args[2] == DocumentFormat.PDF

    def test_generate_demand_missing_required_fields(self, client: TestClient):
        """Test document generation with missing required fields"""
        response = client.post(
            "/api/generate-demand",
            json={
                "case_id": 0,
                # Missing other required fields
            }
        )

        assert response.status_code == 422  # Validation error

    def test_generate_demand_invalid_format(self, client: TestClient):
        """Test document generation with invalid format"""
        response = client.post(
            "/api/generate-demand",
            json={
                "case_id": 0,
                "case_title": "Test",
                "case_description": "Test description",
                "case_details": ["Feature 1"],
                "locale": "en",
                "format": "invalid_format"
            }
        )

        assert response.status_code == 422  # Validation error

    def test_generate_demand_ai_service_error(self, client: TestClient):
        """Test handling of AI service errors"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.side_effect = AIServiceError(
                "AI service unavailable"
            )
            mock_get_service.return_value = mock_ai_service

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Test",
                    "case_description": "Test description",
                    "case_details": ["Feature 1"],
                    "locale": "en",
                    "format": "pdf"
                }
            )

            assert response.status_code == 500
            data = response.json()
            assert "AI service unavailable" in data["detail"]["message"]

    def test_generate_demand_document_service_error(self, client: TestClient, sample_document_content):
        """Test handling of document service errors"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.side_effect = Exception("Document generation failed")

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Test",
                    "case_description": "Test description",
                    "case_details": ["Feature 1"],
                    "locale": "en",
                    "format": "pdf"
                }
            )

            assert response.status_code == 500
            data = response.json()
            assert "Document generation failed" in data["detail"]["message"]

    @pytest.mark.asyncio
    async def test_generate_demand_async(self, async_client: AsyncClient, sample_document_content):
        """Test document generation using async client"""
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "async-test-document.pdf"

            response = await async_client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Async Test",
                    "case_description": "Async test description",
                    "case_details": ["Async feature 1", "Async feature 2"],
                    "locale": "en",
                    "format": "pdf"
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert "async-test-document.pdf" in data["document_url"]

    def test_generate_demand_chinese_content(self, client: TestClient):
        """Test document generation with Chinese content"""
        chinese_content = """
# 项目需求文档

## 项目概述
这是一个中文项目需求文档。

## 功能需求
1. 用户管理
2. 商品管理
3. 订单处理
"""
        
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = chinese_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "chinese-document.pdf"

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "中文项目",
                    "case_description": "中文项目描述",
                    "case_details": ["功能一", "功能二"],
                    "locale": "zh",
                    "format": "pdf"
                }
            )

            assert response.status_code == 200
            data = response.json()
            assert "chinese-document.pdf" in data["document_url"]
            assert "项目需求文档" in data["content"]

    def test_generate_demand_large_case_details(self, client: TestClient, sample_document_content):
        """Test document generation with large case details"""
        large_details = [f"Feature {i}: Very detailed description of feature {i}" for i in range(50)]
        
        with patch('app.routers.documents.get_ai_service') as mock_get_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_document.return_value = sample_document_content
            mock_get_service.return_value = mock_ai_service
            
            mock_doc_service.save_document.return_value = "large-document.pdf"

            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Large Project",
                    "case_description": "Project with many features",
                    "case_details": large_details,
                    "locale": "en",
                    "format": "pdf"
                }
            )

            assert response.status_code == 200
            # Verify the case data was passed correctly to AI service
            call_args = mock_ai_service.generate_document.call_args[0][0]
            assert isinstance(call_args, CaseData)
            assert len(call_args.details) == 50
