#!/usr/bin/env python3
"""
应用集成测试脚本

测试 SQLite 数据库与 FastAPI 应用的集成，验证数据库迁移是否成功。
"""

import os
import sys
import tempfile
import asyncio
from pathlib import Path

# 添加项目路径到 Python 路径
project_root = Path(__file__).parent / "findu-backend"
sys.path.insert(0, str(project_root))

# 设置测试环境变量
os.environ["SQLITE_DB_PATH"] = tempfile.mktemp(suffix='.db')
os.environ["ENABLE_DATABASE"] = "true"
os.environ["JWT_SECRET"] = "test_secret"
os.environ["AI_API_KEY"] = "test_key"

from fastapi.testclient import TestClient


def test_database_integration():
    """测试数据库集成"""
    print("🔍 测试数据库集成...")
    
    try:
        # 导入数据库模块
        from app.db import get_database, init_database
        
        # 初始化数据库
        success = init_database()
        if not success:
            print("❌ 数据库初始化失败")
            return False
        
        # 获取数据库实例
        db = get_database()
        if db is None:
            print("❌ 无法获取数据库实例")
            return False
        
        print("✅ 数据库集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {str(e)}")
        return False


def test_app_startup():
    """测试应用启动"""
    print("\n🔍 测试应用启动...")
    
    try:
        # 导入主应用
        from main import app
        
        # 创建测试客户端
        client = TestClient(app)
        
        # 测试健康检查端点
        response = client.get("/health")
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "healthy":
                print("✅ 应用启动测试通过")
                return True
            else:
                print(f"❌ 健康检查失败: {data}")
                return False
        else:
            print(f"❌ 健康检查请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 应用启动测试失败: {str(e)}")
        return False


def test_api_with_database():
    """测试API与数据库的集成"""
    print("\n🔍 测试API与数据库集成...")

    try:
        from main import app
        client = TestClient(app)

        # 测试案例生成API
        test_data = {
            "prompt": "我需要一个简单的博客系统",
            "locale": "zh"
        }

        response = client.post("/api/generate-cases", json=test_data)

        # 由于没有有效的AI API密钥，我们期望503错误
        if response.status_code == 503:
            data = response.json()
            if "detail" in data and "error" in data["detail"]:
                error_type = data["detail"]["error"]
                if error_type == "ai_api_error":
                    print("✅ API与数据库集成测试通过 (AI API密钥无效，符合预期)")
                    return True
        elif response.status_code == 200:
            data = response.json()
            if "cases" in data and len(data["cases"]) > 0:
                print("✅ API与数据库集成测试通过")
                return True
            else:
                print(f"❌ API返回数据异常: {data}")
                return False

        print(f"❌ API请求失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return False

    except Exception as e:
        print(f"❌ API与数据库集成测试失败: {str(e)}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print("\n🔍 测试数据库操作...")
    
    try:
        from app.db import get_database
        
        db = get_database()
        if db is None:
            print("⚠️  数据库功能未启用，跳过数据库操作测试")
            return True
        
        # 测试保存提示词
        prompt_id = db.save_prompt("test_user", "测试需求", "zh")
        if prompt_id is None:
            print("❌ 保存提示词失败")
            return False
        
        # 测试保存案例
        case_data = {
            "id": 0,
            "title": "测试案例",
            "description": "测试描述",
            "details": ["功能1", "功能2"]
        }
        case_id = db.save_case(prompt_id, case_data)
        if case_id is None:
            print("❌ 保存案例失败")
            return False
        
        # 测试获取用户历史
        history = db.get_user_history("test_user", limit=5)
        if len(history) == 0:
            print("❌ 获取用户历史失败")
            return False
        
        print("✅ 数据库操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {str(e)}")
        return False


def test_sqlite_file_creation():
    """测试SQLite文件创建"""
    print("\n🔍 测试SQLite文件创建...")
    
    try:
        db_path = os.environ.get("SQLITE_DB_PATH")
        if db_path and os.path.exists(db_path):
            print(f"✅ SQLite数据库文件已创建: {db_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(db_path)
            print(f"📊 数据库文件大小: {file_size} bytes")
            
            return True
        else:
            print("❌ SQLite数据库文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ SQLite文件创建测试失败: {str(e)}")
        return False


def cleanup():
    """清理测试文件"""
    try:
        db_path = os.environ.get("SQLITE_DB_PATH")
        if db_path and os.path.exists(db_path):
            os.unlink(db_path)
            print(f"🧹 清理测试数据库文件: {db_path}")
    except Exception as e:
        print(f"⚠️  清理失败: {str(e)}")


def main():
    """主函数"""
    print("🚀 开始应用集成测试...")
    print(f"测试数据库路径: {os.environ.get('SQLITE_DB_PATH')}")
    
    tests = [
        ("数据库集成", test_database_integration),
        ("应用启动", test_app_startup),
        ("API与数据库集成", test_api_with_database),
        ("数据库操作", test_database_operations),
        ("SQLite文件创建", test_sqlite_file_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    try:
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"运行测试: {test_name}")
            print('='*50)
            
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {str(e)}")
        
        print(f"\n{'='*50}")
        print(f"📊 测试结果: {passed}/{total} 通过")
        print('='*50)
        
        if passed == total:
            print("🎉 所有测试通过！SQLite数据库迁移成功")
            success = True
        else:
            print("⚠️  部分测试失败，请检查配置")
            success = False
            
    finally:
        cleanup()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
