#!/usr/bin/env python3
"""
Docker 部署测试脚本

测试 SQLite 数据库在 Docker 环境中的部署和功能。
"""

import requests
import time
import sys
import json
from typing import Dict, Any


class DockerDeploymentTester:
    """Docker 部署测试类"""
    
    def __init__(self, base_url: str = "http://localhost"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def wait_for_service(self, endpoint: str, max_wait: int = 60) -> bool:
        """等待服务启动"""
        print(f"等待服务启动: {endpoint}")
        
        for i in range(max_wait):
            try:
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ 服务已启动 ({i+1}s)")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            if (i + 1) % 10 == 0:
                print(f"⏳ 等待中... ({i+1}s)")
        
        print(f"❌ 服务启动超时 ({max_wait}s)")
        return False
    
    def test_health_check(self) -> bool:
        """测试健康检查端点"""
        print("\n🔍 测试健康检查...")
        
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    print("✅ 健康检查通过")
                    return True
                else:
                    print(f"❌ 健康检查失败: {data}")
                    return False
            else:
                print(f"❌ 健康检查请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False
    
    def test_api_endpoints(self) -> bool:
        """测试主要API端点"""
        print("\n🔍 测试API端点...")
        
        # 测试案例生成端点
        try:
            test_data = {
                "prompt": "我需要一个简单的博客系统",
                "locale": "zh"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/generate-cases",
                json=test_data,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if "cases" in data and len(data["cases"]) > 0:
                    print("✅ 案例生成API正常")
                    return True
                else:
                    print(f"❌ 案例生成API返回数据异常: {data}")
                    return False
            else:
                print(f"❌ 案例生成API请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 案例生成API异常: {str(e)}")
            return False
    
    def test_database_functionality(self) -> bool:
        """测试数据库功能（如果启用）"""
        print("\n🔍 测试数据库功能...")
        
        # 这里可以添加更多数据库相关的测试
        # 由于数据库功能默认关闭，这里主要测试服务是否正常运行
        try:
            # 发送多个请求测试数据库连接稳定性
            for i in range(3):
                test_data = {
                    "prompt": f"测试需求 {i+1}",
                    "locale": "zh"
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/generate-cases",
                    json=test_data,
                    timeout=15
                )
                
                if response.status_code != 200:
                    print(f"❌ 数据库功能测试失败 (请求 {i+1}): {response.status_code}")
                    return False
                
                time.sleep(1)
            
            print("✅ 数据库功能测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据库功能测试异常: {str(e)}")
            return False
    
    def test_static_files(self) -> bool:
        """测试静态文件服务"""
        print("\n🔍 测试静态文件服务...")
        
        try:
            # 测试静态文件目录是否可访问
            response = self.session.get(f"{self.base_url}/static/", timeout=10)
            # 静态文件目录可能返回403或404，这是正常的
            if response.status_code in [200, 403, 404]:
                print("✅ 静态文件服务正常")
                return True
            else:
                print(f"❌ 静态文件服务异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 静态文件服务测试异常: {str(e)}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 开始 Docker 部署测试...")
        print(f"测试目标: {self.base_url}")
        
        # 等待服务启动
        if not self.wait_for_service("/health"):
            return False
        
        # 运行各项测试
        tests = [
            ("健康检查", self.test_health_check),
            ("API端点", self.test_api_endpoints),
            ("数据库功能", self.test_database_functionality),
            ("静态文件", self.test_static_files),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    print(f"❌ {test_name} 测试失败")
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {str(e)}")
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！Docker 部署成功")
            return True
        else:
            print("⚠️  部分测试失败，请检查部署配置")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Docker 部署测试")
    parser.add_argument(
        "--url", 
        default="http://localhost", 
        help="测试目标URL (默认: http://localhost)"
    )
    parser.add_argument(
        "--wait", 
        type=int, 
        default=60, 
        help="等待服务启动的最大时间 (秒)"
    )
    
    args = parser.parse_args()
    
    tester = DockerDeploymentTester(args.url)
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
