"""
Tests for the document service module
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import patch, Mock

from app.services.document_service import DocumentService, DocumentFormat


@pytest.mark.unit
class TestDocumentService:
    """Test cases for DocumentService class"""

    def test_document_service_initialization(self):
        """Test document service initialization"""
        service = DocumentService()
        assert service is not None

    def test_parse_markdown_content_basic(self, mock_document_service):
        """Test basic markdown content parsing"""
        content = """
# Main Title

## Section Title

This is a paragraph.

### Subsection

Another paragraph with **bold** text.

- List item 1
- List item 2
"""
        
        parsed = mock_document_service._parse_markdown_content(content)
        
        # Check that we get different types of content
        types = [item['type'] for item in parsed]
        assert 'heading1' in types
        assert 'heading2' in types
        assert 'heading3' in types
        assert 'paragraph' in types

    def test_parse_markdown_content_empty(self, mock_document_service):
        """Test parsing empty markdown content"""
        parsed = mock_document_service._parse_markdown_content("")
        assert len(parsed) == 0

    def test_parse_markdown_content_only_text(self, mock_document_service):
        """Test parsing plain text content"""
        content = "This is just plain text without any markdown."
        parsed = mock_document_service._parse_markdown_content(content)
        
        assert len(parsed) == 1
        assert parsed[0]['type'] == 'paragraph'
        assert parsed[0]['content'] == content

    def test_generate_txt_basic(self, mock_document_service, sample_document_content):
        """Test basic TXT document generation"""
        result = mock_document_service.generate_txt(sample_document_content)
        
        assert isinstance(result, bytes)
        assert len(result) > 0
        
        # Decode and check content
        text_content = result.decode('utf-8')
        assert "项目需求文档" in text_content
        assert "功能需求" in text_content

    def test_generate_txt_with_custom_title(self, mock_document_service):
        """Test TXT generation with custom title"""
        content = "# Test Document\n\nThis is test content."
        title = "Custom Document Title"
        
        result = mock_document_service.generate_txt(content, title)
        text_content = result.decode('utf-8')
        
        assert title in text_content

    def test_generate_txt_empty_content(self, mock_document_service):
        """Test TXT generation with empty content"""
        result = mock_document_service.generate_txt("")
        
        assert isinstance(result, bytes)
        text_content = result.decode('utf-8')
        assert "FindU 项目需求文档" in text_content  # Default title

    @pytest.mark.skipif(True, reason="PDF generation requires reportlab")
    def test_generate_pdf_basic(self, mock_document_service, sample_document_content):
        """Test basic PDF document generation"""
        result = mock_document_service.generate_pdf(sample_document_content)
        
        assert isinstance(result, bytes)
        assert len(result) > 0
        assert result.startswith(b'%PDF')  # PDF file signature

    @pytest.mark.skipif(True, reason="DOCX generation requires python-docx")
    def test_generate_docx_basic(self, mock_document_service, sample_document_content):
        """Test basic DOCX document generation"""
        result = mock_document_service.generate_docx(sample_document_content)
        
        assert isinstance(result, bytes)
        assert len(result) > 0

    def test_generate_document_pdf_format(self, mock_document_service, sample_document_content):
        """Test document generation with PDF format"""
        with patch.object(mock_document_service, 'generate_pdf') as mock_pdf:
            mock_pdf.return_value = b'fake pdf content'
            
            data, mime_type = mock_document_service.generate_document(
                sample_document_content, DocumentFormat.PDF
            )
            
            assert data == b'fake pdf content'
            assert mime_type == "application/pdf"
            mock_pdf.assert_called_once()

    def test_generate_document_docx_format(self, mock_document_service, sample_document_content):
        """Test document generation with DOCX format"""
        with patch.object(mock_document_service, 'generate_docx') as mock_docx:
            mock_docx.return_value = b'fake docx content'
            
            data, mime_type = mock_document_service.generate_document(
                sample_document_content, DocumentFormat.DOCX
            )
            
            assert data == b'fake docx content'
            assert mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            mock_docx.assert_called_once()

    def test_generate_document_txt_format(self, mock_document_service, sample_document_content):
        """Test document generation with TXT format"""
        with patch.object(mock_document_service, 'generate_txt') as mock_txt:
            mock_txt.return_value = b'fake txt content'
            
            data, mime_type = mock_document_service.generate_document(
                sample_document_content, DocumentFormat.TXT
            )
            
            assert data == b'fake txt content'
            assert mime_type == "text/plain; charset=utf-8"
            mock_txt.assert_called_once()

    def test_generate_document_invalid_format(self, mock_document_service, sample_document_content):
        """Test document generation with invalid format"""
        with pytest.raises(ValueError, match="不支持的文档格式"):
            mock_document_service.generate_document(
                sample_document_content, "invalid_format"
            )

    def test_save_document_success(self, mock_document_service, temp_static_dir, sample_document_content):
        """Test successful document saving"""
        with patch.object(mock_document_service, 'generate_document') as mock_generate:
            mock_generate.return_value = (b'fake content', 'application/pdf')
            
            filename = mock_document_service.save_document(
                sample_document_content, "Test Document", DocumentFormat.PDF
            )
            
            assert filename.endswith('.pdf')
            assert (temp_static_dir / filename).exists()

    def test_save_document_creates_directory(self, mock_document_service, sample_document_content):
        """Test that save_document creates directory if it doesn't exist"""
        with tempfile.TemporaryDirectory() as temp_dir:
            non_existent_dir = Path(temp_dir) / "non_existent" / "demands"
            
            with patch('app.services.document_service.STATIC_DIR', non_existent_dir):
                with patch.object(mock_document_service, 'generate_document') as mock_generate:
                    mock_generate.return_value = (b'fake content', 'application/pdf')
                    
                    filename = mock_document_service.save_document(
                        sample_document_content, "Test", DocumentFormat.PDF
                    )
                    
                    assert non_existent_dir.exists()
                    assert (non_existent_dir / filename).exists()

    def test_save_document_file_write_error(self, mock_document_service, temp_static_dir, sample_document_content):
        """Test handling of file write errors"""
        with patch.object(mock_document_service, 'generate_document') as mock_generate:
            mock_generate.return_value = (b'fake content', 'application/pdf')
            
            # Make the directory read-only to cause write error
            temp_static_dir.chmod(0o444)
            
            try:
                with pytest.raises(Exception):
                    mock_document_service.save_document(
                        sample_document_content, "Test", DocumentFormat.PDF
                    )
            finally:
                # Restore permissions for cleanup
                temp_static_dir.chmod(0o755)

    def test_clean_filename_basic(self, mock_document_service):
        """Test basic filename cleaning"""
        title = "My Project Document"
        cleaned = mock_document_service._clean_filename(title)
        
        assert cleaned == "My_Project_Document"

    def test_clean_filename_special_characters(self, mock_document_service):
        """Test filename cleaning with special characters"""
        title = "Project/Document: Version 2.0 (Final)!"
        cleaned = mock_document_service._clean_filename(title)
        
        # Should remove or replace special characters
        assert "/" not in cleaned
        assert ":" not in cleaned
        assert "(" not in cleaned
        assert ")" not in cleaned
        assert "!" not in cleaned

    def test_clean_filename_chinese_characters(self, mock_document_service):
        """Test filename cleaning with Chinese characters"""
        title = "项目需求文档"
        cleaned = mock_document_service._clean_filename(title)
        
        # Should handle Chinese characters appropriately
        assert len(cleaned) > 0

    def test_clean_filename_empty(self, mock_document_service):
        """Test filename cleaning with empty string"""
        cleaned = mock_document_service._clean_filename("")
        
        assert cleaned == "document"  # Should provide default

    def test_clean_filename_long_title(self, mock_document_service):
        """Test filename cleaning with very long title"""
        long_title = "A" * 200  # Very long title
        cleaned = mock_document_service._clean_filename(long_title)
        
        # Should be truncated to reasonable length
        assert len(cleaned) <= 100

    def test_document_format_enum(self):
        """Test DocumentFormat enum values"""
        assert DocumentFormat.PDF == "pdf"
        assert DocumentFormat.DOCX == "docx"
        assert DocumentFormat.TXT == "txt"

    def test_markdown_parsing_edge_cases(self, mock_document_service):
        """Test markdown parsing with edge cases"""
        # Test with multiple consecutive newlines
        content = "# Title\n\n\n\nParagraph after many newlines"
        parsed = mock_document_service._parse_markdown_content(content)
        
        # Should handle gracefully
        assert len(parsed) >= 2

        # Test with mixed line endings
        content_mixed = "# Title\r\n\r\nParagraph\n\nAnother paragraph\r\n"
        parsed_mixed = mock_document_service._parse_markdown_content(content_mixed)
        
        assert len(parsed_mixed) >= 2

    def test_document_generation_with_unicode(self, mock_document_service):
        """Test document generation with Unicode content"""
        unicode_content = """
# 项目需求文档 📋

## 功能需求 🚀

这是一个包含 Unicode 字符的文档：
- 表情符号：😀 🎉 ✨
- 特殊字符：© ® ™
- 数学符号：∑ ∆ π

### 技术要求 💻

支持多语言：English, 中文, العربية, русский
"""
        
        result = mock_document_service.generate_txt(unicode_content)
        text_content = result.decode('utf-8')
        
        assert "项目需求文档" in text_content
        assert "😀" in text_content
        assert "English" in text_content
        assert "العربية" in text_content
