"""
Integration tests for the FindU backend
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from main import app


@pytest.mark.integration
class TestAPIIntegration:
    """Integration tests for API endpoints"""

    def test_health_check(self, client: TestClient):
        """Test health check endpoint"""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy"}

    @pytest.mark.asyncio
    async def test_full_workflow_success(self, async_client: AsyncClient):
        """Test complete workflow from case generation to document creation"""
        # Mock AI service responses
        mock_cases = [
            {
                "id": 0,
                "title": "E-commerce Platform",
                "description": "Comprehensive online shopping solution",
                "details": [
                    "User registration and authentication",
                    "Product catalog management",
                    "Shopping cart functionality",
                    "Payment processing",
                    "Order management"
                ]
            }
        ]
        
        mock_document_content = """
# E-commerce Platform Requirements

## Project Overview
This document outlines the requirements for a comprehensive e-commerce platform.

## Functional Requirements

### 1. User Management
- User registration and authentication
- Profile management
- Password recovery

### 2. Product Management
- Product catalog
- Search and filtering
- Product details

### 3. Shopping Cart
- Add/remove items
- Quantity management
- Cart persistence

### 4. Payment Processing
- Multiple payment methods
- Secure transactions
- Payment confirmation

### 5. Order Management
- Order creation
- Status tracking
- Order history
"""

        with patch('app.routers.cases.get_ai_service') as mock_cases_service, \
             patch('app.routers.documents.get_ai_service') as mock_docs_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            # Setup mocks
            mock_ai_cases = AsyncMock()
            mock_ai_cases.generate_cases.return_value = mock_cases
            mock_cases_service.return_value = mock_ai_cases
            
            mock_ai_docs = AsyncMock()
            mock_ai_docs.generate_document.return_value = mock_document_content
            mock_docs_service.return_value = mock_ai_docs
            
            mock_doc_service.save_document.return_value = "test-document.pdf"

            # Step 1: Generate cases
            cases_response = await async_client.post(
                "/api/generate-cases",
                json={
                    "prompt": "I want to build an e-commerce platform",
                    "locale": "en"
                }
            )
            
            assert cases_response.status_code == 200
            cases_data = cases_response.json()
            assert "cases" in cases_data
            assert len(cases_data["cases"]) == 1
            
            selected_case = cases_data["cases"][0]
            
            # Step 2: Generate document
            document_response = await async_client.post(
                "/api/generate-demand",
                json={
                    "case_id": selected_case["id"],
                    "case_title": selected_case["title"],
                    "case_description": selected_case["description"],
                    "case_details": selected_case["details"],
                    "locale": "en",
                    "format": "pdf"
                }
            )
            
            assert document_response.status_code == 200
            document_data = document_response.json()
            assert "document_url" in document_data
            assert "content" in document_data
            assert "test-document.pdf" in document_data["document_url"]

    def test_api_error_handling_chain(self, client: TestClient):
        """Test error handling across multiple API calls"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            # First call succeeds
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {
                    "id": 0,
                    "title": "Test Project",
                    "description": "Test description",
                    "details": ["Feature 1"]
                }
            ]
            mock_service.return_value = mock_ai_service
            
            # Generate cases successfully
            response1 = client.post(
                "/api/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )
            assert response1.status_code == 200
            
            # Second call fails
            from app.services.ai_service import AIServiceError
            mock_ai_service.generate_cases.side_effect = AIServiceError("Service down")
            
            response2 = client.post(
                "/api/generate-cases",
                json={"prompt": "another prompt", "locale": "en"}
            )
            assert response2.status_code == 500

    @pytest.mark.asyncio
    async def test_concurrent_requests(self, async_client: AsyncClient):
        """Test handling of concurrent requests"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            # Create multiple concurrent requests
            tasks = []
            for i in range(5):
                task = async_client.post(
                    "/api/generate-cases",
                    json={"prompt": f"test prompt {i}", "locale": "en"}
                )
                tasks.append(task)
            
            # Wait for all requests to complete
            responses = await asyncio.gather(*tasks)
            
            # All should succeed
            for response in responses:
                assert response.status_code == 200
                assert "cases" in response.json()

    def test_request_validation_integration(self, client: TestClient):
        """Test request validation across different endpoints"""
        # Test cases endpoint validation
        invalid_cases_requests = [
            {},  # Missing prompt
            {"prompt": ""},  # Empty prompt
            {"prompt": "test", "locale": "invalid"},  # Invalid locale
            {"invalid_field": "value"}  # Invalid field
        ]
        
        for invalid_request in invalid_cases_requests:
            response = client.post("/api/generate-cases", json=invalid_request)
            assert response.status_code in [400, 422]
        
        # Test documents endpoint validation
        invalid_docs_requests = [
            {},  # Missing all fields
            {"case_id": 0},  # Missing other required fields
            {"case_id": "invalid", "case_title": "Test"},  # Invalid case_id type
            {"case_id": 0, "case_title": "Test", "case_description": "Test", 
             "case_details": "not_a_list", "locale": "en"}  # Invalid details type
        ]
        
        for invalid_request in invalid_docs_requests:
            response = client.post("/api/generate-demand", json=invalid_request)
            assert response.status_code in [400, 422]

    @pytest.mark.asyncio
    async def test_large_payload_handling(self, async_client: AsyncClient):
        """Test handling of large payloads"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            # Create a very long prompt
            long_prompt = "I want to build " + "a very detailed " * 1000 + "application"
            
            response = await async_client.post(
                "/api/generate-cases",
                json={"prompt": long_prompt, "locale": "en"}
            )
            
            # Should handle large payloads gracefully
            assert response.status_code in [200, 413, 422]  # Success or payload too large

    def test_content_type_handling(self, client: TestClient):
        """Test different content types"""
        # Valid JSON
        response = client.post(
            "/api/generate-cases",
            json={"prompt": "test", "locale": "en"}
        )
        # Should be handled by mocked service or return appropriate error
        assert response.status_code in [200, 500]
        
        # Invalid content type
        response = client.post(
            "/api/generate-cases",
            data="prompt=test&locale=en",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422
        
        # No content type
        response = client.post(
            "/api/generate-cases",
            data='{"prompt": "test", "locale": "en"}'
        )
        assert response.status_code in [422, 415]

    @pytest.mark.asyncio
    async def test_timeout_handling(self, async_client: AsyncClient):
        """Test timeout handling in API calls"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            
            # Simulate a slow AI service
            async def slow_generate_cases(*args, **kwargs):
                await asyncio.sleep(2)  # Simulate slow response
                return [{"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}]
            
            mock_ai_service.generate_cases = slow_generate_cases
            mock_service.return_value = mock_ai_service
            
            # Make request with timeout
            try:
                response = await asyncio.wait_for(
                    async_client.post(
                        "/api/generate-cases",
                        json={"prompt": "test", "locale": "en"}
                    ),
                    timeout=1.0  # 1 second timeout
                )
                # If it completes within timeout, should be successful
                assert response.status_code == 200
            except asyncio.TimeoutError:
                # Timeout is expected behavior for slow services
                pass

    def test_cors_headers(self, client: TestClient):
        """Test CORS headers are properly set"""
        response = client.options("/api/generate-cases")
        
        # Should have CORS headers (if CORS is properly configured)
        # The exact headers depend on the CORS configuration
        assert response.status_code in [200, 405]  # OPTIONS might not be explicitly handled

    @pytest.mark.asyncio
    async def test_error_response_format(self, async_client: AsyncClient):
        """Test that error responses follow consistent format"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            from app.services.ai_service import AIAPIError
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.side_effect = AIAPIError(
                "API Error", "test_provider", 401
            )
            mock_service.return_value = mock_ai_service
            
            response = await async_client.post(
                "/api/generate-cases",
                json={"prompt": "test", "locale": "en"}
            )
            
            assert response.status_code == 503
            error_data = response.json()
            
            # Check error response structure
            assert "detail" in error_data
            detail = error_data["detail"]
            assert "error" in detail
            assert "message" in detail
            assert detail["error"] == "ai_api_error"

    def test_static_file_endpoints(self, client: TestClient, temp_static_dir):
        """Test static file serving endpoints"""
        # Create a test file
        test_file = temp_static_dir / "test-document.txt"
        test_file.write_text("Test document content")
        
        # Test file listing
        response = client.get("/api/files/")
        assert response.status_code == 200
        files_data = response.json()
        assert "files" in files_data
        
        # Test file download
        response = client.get(f"/api/files/download/test-document.txt")
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/plain; charset=utf-8"
