"""
Performance tests for the FindU backend
"""

import pytest
import time
import async<PERSON>
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


@pytest.mark.slow
@pytest.mark.performance
class TestPerformance:
    """Performance tests for API endpoints"""

    def test_cases_api_response_time(self, client: TestClient):
        """Test response time for cases API"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            start_time = time.time()
            response = client.post(
                "/api/generate-cases",
                json={"prompt": "test prompt", "locale": "en"}
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 5.0  # Should respond within 5 seconds

    def test_document_api_response_time(self, client: TestClient):
        """Test response time for document generation API"""
        with patch('app.routers.documents.get_ai_service') as mock_ai_service, \
             patch('app.routers.documents.document_service') as mock_doc_service:
            
            mock_ai = AsyncMock()
            mock_ai.generate_document.return_value = "# Test Document\n\nContent here."
            mock_ai_service.return_value = mock_ai
            
            mock_doc_service.save_document.return_value = "test-doc.pdf"
            
            start_time = time.time()
            response = client.post(
                "/api/generate-demand",
                json={
                    "case_id": 0,
                    "case_title": "Test",
                    "case_description": "Test description",
                    "case_details": ["Feature 1"],
                    "locale": "en",
                    "format": "pdf"
                }
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < 10.0  # Should respond within 10 seconds

    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, async_client: AsyncClient):
        """Test performance under concurrent load"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            # Test with 10 concurrent requests
            num_requests = 10
            start_time = time.time()
            
            tasks = []
            for i in range(num_requests):
                task = async_client.post(
                    "/api/generate-cases",
                    json={"prompt": f"test prompt {i}", "locale": "en"}
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time_per_request = total_time / num_requests
            
            # All requests should succeed
            for response in responses:
                assert response.status_code == 200
            
            # Average time per request should be reasonable
            assert avg_time_per_request < 2.0  # Less than 2 seconds per request on average
            assert total_time < 15.0  # Total time should be less than 15 seconds

    def test_memory_usage_large_payload(self, client: TestClient):
        """Test memory usage with large payloads"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            # Create large payload
            large_prompt = "I want to build " + "a detailed " * 10000 + "application"
            
            response = client.post(
                "/api/generate-cases",
                json={"prompt": large_prompt, "locale": "en"}
            )
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # Memory increase should be reasonable (less than 100MB for this test)
            assert memory_increase < 100
            assert response.status_code in [200, 413, 422]  # Success or payload too large

    def test_database_connection_performance(self, mock_mongodb):
        """Test database operation performance"""
        # Test multiple database operations
        start_time = time.time()
        
        for i in range(100):
            mock_mongodb.save_prompt(f"test prompt {i}", "en")
            mock_mongodb.save_case(f"prompt_id_{i}", {
                "id": i,
                "title": f"Test Case {i}",
                "description": "Test description",
                "details": ["Feature 1", "Feature 2"]
            })
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 100 operations should complete quickly
        assert total_time < 1.0  # Less than 1 second for 100 mock operations

    @pytest.mark.asyncio
    async def test_ai_service_timeout_performance(self):
        """Test AI service timeout handling performance"""
        from app.services.ai_service import AIService, AIConfig
        
        config = AIConfig(
            api_key="test_key",
            base_url="https://api.test.com/v1",
            model="test-model",
            timeout=1.0  # Very short timeout
        )
        
        service = AIService(config)
        
        # Mock a slow AI response
        with patch.object(service, '_make_chat_completion') as mock_completion:
            async def slow_completion(*args, **kwargs):
                await asyncio.sleep(2.0)  # Slower than timeout
                return "response"
            
            mock_completion.side_effect = slow_completion
            
            start_time = time.time()
            
            try:
                await service.generate_cases("test prompt", "en")
            except Exception:
                pass  # Expected to timeout
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # Should timeout quickly, not wait for the full 2 seconds
            assert elapsed_time < 1.5  # Should be close to the 1.0 second timeout

    def test_static_file_serving_performance(self, client: TestClient, temp_static_dir):
        """Test static file serving performance"""
        # Create test files of different sizes
        small_file = temp_static_dir / "small.txt"
        medium_file = temp_static_dir / "medium.txt"
        large_file = temp_static_dir / "large.txt"
        
        small_file.write_text("Small content")
        medium_file.write_text("Medium content " * 1000)
        large_file.write_text("Large content " * 100000)
        
        # Test serving different file sizes
        files_to_test = [
            ("small.txt", 0.1),    # Should be very fast
            ("medium.txt", 0.5),   # Should be fast
            ("large.txt", 2.0),    # Should be reasonable
        ]
        
        for filename, max_time in files_to_test:
            start_time = time.time()
            response = client.get(f"/api/files/download/{filename}")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            assert response.status_code == 200
            assert response_time < max_time

    def test_health_check_performance(self, client: TestClient):
        """Test health check endpoint performance"""
        # Health check should be very fast
        times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            
            times.append(end_time - start_time)
            assert response.status_code == 200
        
        avg_time = sum(times) / len(times)
        max_time = max(times)
        
        # Health check should be very fast
        assert avg_time < 0.1  # Average less than 100ms
        assert max_time < 0.5  # Maximum less than 500ms

    @pytest.mark.asyncio
    async def test_error_handling_performance(self, async_client: AsyncClient):
        """Test that error handling doesn't significantly impact performance"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            from app.services.ai_service import AIServiceError
            
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.side_effect = AIServiceError("Test error")
            mock_service.return_value = mock_ai_service
            
            # Test multiple error responses
            start_time = time.time()
            
            tasks = []
            for i in range(5):
                task = async_client.post(
                    "/api/generate-cases",
                    json={"prompt": f"test {i}", "locale": "en"}
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # All should return errors quickly
            for response in responses:
                assert response.status_code == 500
            
            # Error handling should be fast
            assert total_time < 2.0

    def test_json_parsing_performance(self, client: TestClient):
        """Test JSON parsing performance with large payloads"""
        # Create a large but valid JSON payload
        large_details = [f"Feature {i}: Detailed description of feature {i}" for i in range(1000)]
        
        start_time = time.time()
        response = client.post(
            "/api/generate-demand",
            json={
                "case_id": 0,
                "case_title": "Large Project",
                "case_description": "Project with many features",
                "case_details": large_details,
                "locale": "en",
                "format": "pdf"
            }
        )
        end_time = time.time()
        
        parsing_time = end_time - start_time
        
        # JSON parsing should be fast even for large payloads
        assert parsing_time < 1.0
        assert response.status_code in [200, 422, 500]  # Various valid responses

    @pytest.mark.asyncio
    async def test_throughput_measurement(self, async_client: AsyncClient):
        """Measure API throughput"""
        with patch('app.routers.cases.get_ai_service') as mock_service:
            mock_ai_service = AsyncMock()
            mock_ai_service.generate_cases.return_value = [
                {"id": 0, "title": "Test", "description": "Test", "details": ["Feature"]}
            ]
            mock_service.return_value = mock_ai_service
            
            # Measure requests per second
            duration = 5.0  # Test for 5 seconds
            start_time = time.time()
            request_count = 0
            
            while time.time() - start_time < duration:
                response = await async_client.post(
                    "/api/generate-cases",
                    json={"prompt": f"test {request_count}", "locale": "en"}
                )
                assert response.status_code == 200
                request_count += 1
            
            actual_duration = time.time() - start_time
            requests_per_second = request_count / actual_duration
            
            # Should handle at least 10 requests per second
            assert requests_per_second >= 10.0
            print(f"Throughput: {requests_per_second:.2f} requests/second")
