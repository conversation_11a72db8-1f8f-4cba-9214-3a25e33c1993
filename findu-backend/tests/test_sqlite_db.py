"""
SQLite 数据库功能测试

测试 SQLite 数据库模块的各项功能，确保数据库迁移后所有功能正常工作。
"""

import pytest
import tempfile
import os
from pathlib import Path
from app.db.sqlite import SQLiteDB


class TestSQLiteDB:
    """SQLite 数据库测试类"""

    @pytest.fixture
    def temp_db(self):
        """创建临时数据库用于测试"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            db_path = tmp.name
        
        # 创建数据库实例
        db = SQLiteDB(db_path)
        yield db
        
        # 清理临时文件
        db.close()
        if os.path.exists(db_path):
            os.unlink(db_path)

    def test_database_initialization(self, temp_db):
        """测试数据库初始化"""
        db = temp_db
        
        # 验证数据库文件存在
        assert os.path.exists(db.db_path)
        
        # 验证表结构创建成功
        conn = db._get_connection()
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['prompts', 'cases', 'documents', 'users']
        for table in expected_tables:
            assert table in tables, f"表 {table} 未创建"
        
        cursor.close()

    def test_save_prompt(self, temp_db):
        """测试保存提示词功能"""
        db = temp_db
        
        # 测试数据
        user_id = "test_user_123"
        prompt = "我需要一个电商网站"
        locale = "zh"
        
        # 保存提示词
        prompt_id = db.save_prompt(user_id, prompt, locale)
        
        # 验证返回值
        assert prompt_id is not None
        assert prompt_id.isdigit()
        
        # 验证数据库中的数据
        conn = db._get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM prompts WHERE id = ?", (int(prompt_id),))
        row = cursor.fetchone()
        
        assert row is not None
        assert row['user_id'] == user_id
        assert row['prompt'] == prompt
        assert row['locale'] == locale
        assert row['status'] == 'processed'
        
        cursor.close()

    def test_save_case(self, temp_db):
        """测试保存案例功能"""
        db = temp_db
        
        # 先创建一个提示词
        prompt_id = db.save_prompt("test_user", "测试提示词", "zh")
        
        # 测试案例数据
        case_data = {
            "id": 0,
            "title": "电商网站",
            "description": "在线购物平台",
            "details": ["用户注册", "商品展示", "购物车", "订单管理"]
        }
        
        # 保存案例
        case_id = db.save_case(prompt_id, case_data)
        
        # 验证返回值
        assert case_id is not None
        assert case_id.isdigit()
        
        # 验证数据库中的数据
        conn = db._get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM cases WHERE id = ?", (int(case_id),))
        row = cursor.fetchone()
        
        assert row is not None
        assert row['prompt_id'] == int(prompt_id)
        assert row['case_id'] == case_data['id']
        assert row['title'] == case_data['title']
        assert row['description'] == case_data['description']
        
        # 验证 JSON 数据
        import json
        details = json.loads(row['details'])
        assert details == case_data['details']
        
        cursor.close()

    def test_save_document_record(self, temp_db):
        """测试保存文档记录功能"""
        db = temp_db
        
        # 先创建提示词和案例
        prompt_id = db.save_prompt("test_user", "测试提示词", "zh")
        case_data = {"id": 0, "title": "测试案例", "description": "测试描述", "details": []}
        case_id = db.save_case(prompt_id, case_data)
        
        # 测试文档记录数据
        document_url = "http://example.com/document.pdf"
        locale = "zh"
        
        # 保存文档记录
        doc_id = db.save_document_record(case_id, document_url, locale)
        
        # 验证返回值
        assert doc_id is not None
        assert doc_id.isdigit()
        
        # 验证数据库中的数据
        conn = db._get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM documents WHERE id = ?", (int(doc_id),))
        row = cursor.fetchone()
        
        assert row is not None
        assert row['case_id'] == int(case_id)
        assert row['document_url'] == document_url
        assert row['locale'] == locale
        assert row['download_count'] == 0
        
        cursor.close()

    def test_get_user_history(self, temp_db):
        """测试获取用户历史记录功能"""
        db = temp_db
        
        user_id = "test_user_history"
        
        # 创建多个提示词记录
        prompts = [
            ("第一个需求", "zh"),
            ("第二个需求", "en"),
            ("第三个需求", "zh")
        ]
        
        prompt_ids = []
        for prompt, locale in prompts:
            pid = db.save_prompt(user_id, prompt, locale)
            prompt_ids.append(pid)
        
        # 获取用户历史记录
        history = db.get_user_history(user_id, limit=5)
        
        # 验证结果
        assert len(history) == 3
        
        # 验证数据格式（应该按时间倒序）
        for i, record in enumerate(history):
            assert '_id' in record
            assert record['user_id'] == user_id
            assert 'prompt' in record
            assert 'locale' in record
            assert 'created_at' in record
            assert record['status'] == 'processed'

    def test_get_popular_cases(self, temp_db):
        """测试获取热门案例功能"""
        db = temp_db
        
        # 创建多个案例
        cases_data = [
            ("电商网站", "zh", {"id": 0, "title": "电商网站", "description": "在线购物", "details": ["功能1"]}),
            ("博客系统", "zh", {"id": 1, "title": "博客系统", "description": "内容管理", "details": ["功能2"]}),
            ("CRM系统", "en", {"id": 2, "title": "CRM System", "description": "Customer Management", "details": ["Feature1"]})
        ]
        
        for prompt_text, locale, case_data in cases_data:
            prompt_id = db.save_prompt("test_user", prompt_text, locale)
            db.save_case(prompt_id, case_data)
        
        # 获取中文热门案例
        popular_cases_zh = db.get_popular_cases(locale="zh", limit=5)
        
        # 验证结果
        assert len(popular_cases_zh) == 2  # 只有2个中文案例
        
        for case in popular_cases_zh:
            assert '_id' in case
            assert 'prompt_id' in case
            assert 'title' in case
            assert 'description' in case
            assert 'details' in case
            assert isinstance(case['details'], list)
        
        # 获取英文热门案例
        popular_cases_en = db.get_popular_cases(locale="en", limit=5)
        assert len(popular_cases_en) == 1  # 只有1个英文案例

    def test_database_connection_thread_safety(self, temp_db):
        """测试数据库连接的线程安全性"""
        import threading
        import time
        
        db = temp_db
        results = []
        errors = []
        
        def worker(worker_id):
            try:
                for i in range(5):
                    prompt_id = db.save_prompt(f"user_{worker_id}", f"需求_{i}", "zh")
                    results.append(prompt_id)
                    time.sleep(0.01)  # 模拟一些处理时间
            except Exception as e:
                errors.append(str(e))
        
        # 创建多个线程同时操作数据库
        threads = []
        for i in range(3):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"线程安全测试出现错误: {errors}"
        assert len(results) == 15  # 3个线程 × 5次操作
        
        # 验证所有记录都成功保存
        for prompt_id in results:
            assert prompt_id is not None
            assert prompt_id.isdigit()

    def test_database_close(self, temp_db):
        """测试数据库连接关闭"""
        db = temp_db
        
        # 确保连接存在
        conn = db._get_connection()
        assert conn is not None
        
        # 关闭数据库
        db.close()
        
        # 验证连接已关闭
        assert not hasattr(db._local, 'connection')
