/**
 * 404 错误页面组件
 *
 * 当用户访问不存在的页面时显示的错误页面。
 * 提供友好的错误信息和返回首页的链接。
 *
 * 功能特性：
 * - 多语言支持
 * - 友好的错误提示
 * - 返回首页链接
 */

import Link from 'next/link';

/**
 * 404 错误页面组件
 *
 * 显示页面未找到的错误信息，并提供返回首页的链接。
 * 使用默认语言（英文）来避免参数传递问题。
 *
 * @returns React 函数组件
 */
export default function NotFound() {
  return (
    <div className="container mx-auto p-10 text-center">
      <h1 className="text-4xl font-bold mb-4">404 - Page Not Found</h1>
      <p className="text-gray-600 mb-6">The page you are looking for does not exist.</p>
      <div className="space-x-4">
        <Link href="/en" className="text-blue-500 underline">
          Back to Home (English)
        </Link>
        <Link href="/zh" className="text-blue-500 underline">
          返回首页 (中文)
        </Link>
      </div>
    </div>
  );
}