/**
 * 主页面组件
 *
 * 这是应用程序的核心页面，作为服务器组件提供静态生成支持。
 * 主要负责路由参数处理和客户端组件的渲染，实际的交互逻辑
 * 由 HomeClient 组件处理。
 *
 * 功能特性：
 * - 服务器端渲染支持
 * - 静态站点生成 (SSG)
 * - 多语言路由处理
 * - 客户端组件集成
 */

import HomeClientNew from '@/components/HomeClient';

/**
 * 主页面组件
 *
 * 作为服务器组件，处理路由参数并渲染客户端交互组件。
 * 支持 Next.js 的静态生成功能，提供更好的性能和 SEO。
 *
 * @param params - 路由参数，包含当前语言设置
 * @returns React 函数组件
 */
export default async function Home({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  return <HomeClientNew locale={locale} />;
}

/**
 * 静态生成参数函数
 *
 * 为 Next.js 的静态站点生成 (SSG) 提供预定义的路由参数。
 * 这个函数告诉 Next.js 需要为哪些语言生成静态页面。
 *
 * @returns 包含所有支持语言的参数数组
 */
export async function generateStaticParams() {
  return [
    { locale: 'en' },  // 英文页面
    { locale: 'zh' },   // 中文页面
  ];
}