import { test, expect } from '@playwright/test';

test.describe('FindU Homepage', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage
    await page.goto('/en');
  });

  test('should display the main page elements', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/FindU/);
    
    // Check main heading
    await expect(page.getByRole('heading', { name: /AI需求生成器|AI Demand Generator/i })).toBeVisible();
    
    // Check prompt input area
    await expect(page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i)).toBeVisible();
    
    // Check generate button (should be disabled initially)
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    await expect(generateButton).toBeVisible();
    await expect(generateButton).toBeDisabled();
  });

  test('should enable generate button when text is entered', async ({ page }) => {
    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    
    // Initially disabled
    await expect(generateButton).toBeDisabled();
    
    // Type some text
    await promptInput.fill('I want to build an e-commerce website');
    
    // Should be enabled now
    await expect(generateButton).toBeEnabled();
  });

  test('should show loading state when generating cases', async ({ page }) => {
    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    
    // Fill prompt and click generate
    await promptInput.fill('I want to build a blog platform');
    await generateButton.click();
    
    // Should show loading state
    await expect(generateButton).toBeDisabled();
    await expect(page.getByText(/生成中|Generating/i)).toBeVisible();
  });

  test('should display cases after generation', async ({ page }) => {
    // Mock the API response
    await page.route('**/api/generate-cases', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cases: [
            {
              id: 0,
              title: 'E-commerce Website',
              description: 'A comprehensive online shopping platform',
              details: ['User registration and authentication', 'Product catalog management', 'Shopping cart functionality']
            },
            {
              id: 1,
              title: 'Blog Platform',
              description: 'A content management system for blogging',
              details: ['Article creation and editing', 'User comments system', 'SEO optimization']
            }
          ]
        })
      });
    });

    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    
    // Generate cases
    await promptInput.fill('I want to build a website');
    await generateButton.click();
    
    // Wait for cases to appear
    await expect(page.getByText('E-commerce Website')).toBeVisible();
    await expect(page.getByText('Blog Platform')).toBeVisible();
    await expect(page.getByText('User registration and authentication')).toBeVisible();
  });

  test('should allow case selection', async ({ page }) => {
    // Mock the API response
    await page.route('**/api/generate-cases', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cases: [
            {
              id: 0,
              title: 'E-commerce Website',
              description: 'A comprehensive online shopping platform',
              details: ['User registration', 'Product catalog', 'Shopping cart']
            }
          ]
        })
      });
    });

    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    
    // Generate cases
    await promptInput.fill('I want to build an e-commerce website');
    await generateButton.click();
    
    // Wait for case to appear and click it
    const caseCard = page.getByText('E-commerce Website').locator('..');
    await expect(caseCard).toBeVisible();
    await caseCard.click();
    
    // Check if case is selected (visual indication)
    await expect(caseCard).toHaveClass(/selected|ring-2|border-cyan/);
    
    // Generate document button should be enabled
    const generateDocButton = page.getByRole('button', { name: /生成需求文档|Generate Document/i });
    await expect(generateDocButton).toBeEnabled();
  });

  test('should generate and display document', async ({ page }) => {
    // Mock cases API
    await page.route('**/api/generate-cases', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          cases: [
            {
              id: 0,
              title: 'E-commerce Website',
              description: 'Online shopping platform',
              details: ['User registration', 'Product catalog']
            }
          ]
        })
      });
    });

    // Mock document generation API
    await page.route('**/api/generate-demand', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          document_url: '/static/demands/test-document.pdf',
          content: 'Mock document content'
        })
      });
    });

    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    
    // Generate cases
    await promptInput.fill('I want to build an e-commerce website');
    await page.getByRole('button', { name: /生成案例|Generate Cases/i }).click();
    
    // Select case
    await page.getByText('E-commerce Website').locator('..').click();
    
    // Generate document
    await page.getByRole('button', { name: /生成需求文档|Generate Document/i }).click();
    
    // Check document preview appears
    await expect(page.getByText(/需求文档生成完成|Document Generated Successfully/i)).toBeVisible();
    await expect(page.getByRole('link', { name: /下载文档|Download Document/i })).toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error
    await page.route('**/api/generate-cases', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: {
            error: 'ai_api_error',
            message: 'AI service is temporarily unavailable'
          }
        })
      });
    });

    const promptInput = page.getByPlaceholder(/描述您的项目需求|Describe your project requirements/i);
    const generateButton = page.getByRole('button', { name: /生成案例|Generate Cases/i });
    
    // Try to generate cases
    await promptInput.fill('test prompt');
    await generateButton.click();
    
    // Should show error message
    await expect(page.getByText(/AI service is temporarily unavailable|服务暂时不可用/i)).toBeVisible();
  });
});

test.describe('Language Switching', () => {
  test('should switch between languages', async ({ page }) => {
    // Start with English
    await page.goto('/en');
    await expect(page.getByText(/Describe your project requirements/i)).toBeVisible();
    
    // Switch to Chinese
    await page.goto('/zh');
    await expect(page.getByText(/描述您的项目需求/i)).toBeVisible();
  });
});

test.describe('Responsive Design', () => {
  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/en');
    
    // Check that elements are still visible and functional
    await expect(page.getByPlaceholder(/Describe your project requirements/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /Generate Cases/i })).toBeVisible();
  });
});
