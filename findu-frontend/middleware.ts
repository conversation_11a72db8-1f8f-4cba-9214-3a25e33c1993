/**
 * Next.js 中间件文件
 * 
 * 该文件负责处理国际化路由和重定向逻辑，确保：
 * 1. 根路径 "/" 正确重定向到默认语言 "/en"
 * 2. 无效的语言代码重定向到默认语言
 * 3. 避免重定向循环
 * 4. 保持 URL 路径的一致性
 */

import { NextRequest, NextResponse } from 'next/server';

// 支持的语言列表
const locales = ['en', 'zh'];
const defaultLocale = 'en';

/**
 * 获取请求中的首选语言
 * 
 * @param request - Next.js 请求对象
 * @returns 首选语言代码，如果无法确定则返回默认语言
 */
function getLocale(request: NextRequest): string {
  // 从 Accept-Language 头部获取首选语言
  const acceptLanguage = request.headers.get('accept-language');
  
  if (acceptLanguage) {
    // 解析 Accept-Language 头部，查找支持的语言
    const preferredLanguages = acceptLanguage
      .split(',')
      .map(lang => lang.split(';')[0].trim().toLowerCase());
    
    for (const lang of preferredLanguages) {
      // 检查完整匹配（如 'en', 'zh'）
      if (locales.includes(lang)) {
        return lang;
      }
      
      // 检查语言前缀匹配（如 'en-US' -> 'en'）
      const langPrefix = lang.split('-')[0];
      if (locales.includes(langPrefix)) {
        return langPrefix;
      }
    }
  }
  
  return defaultLocale;
}

/**
 * 检查路径是否已包含有效的语言前缀
 * 
 * @param pathname - URL 路径
 * @returns 如果路径包含有效语言前缀则返回 true
 */
function hasValidLocale(pathname: string): boolean {
  const segments = pathname.split('/');
  const firstSegment = segments[1];
  return locales.includes(firstSegment);
}

/**
 * 中间件主函数
 *
 * 处理所有传入请求的国际化路由逻辑
 *
 * @param request - Next.js 请求对象
 * @returns Next.js 响应对象或重定向
 */
export function middleware(request: NextRequest) {
  // 在静态导出模式下跳过中间件
  if (process.env.NODE_ENV === 'production' && process.env.STATIC_EXPORT === 'true') {
    return NextResponse.next();
  }

  const { pathname } = request.nextUrl;
  
  // 跳过 API 路由、静态文件和 Next.js 内部路径
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }
  
  // 处理根路径 "/"
  if (pathname === '/') {
    const locale = getLocale(request);
    const redirectUrl = new URL(`/${locale}`, request.url);
    return NextResponse.redirect(redirectUrl);
  }
  
  // 检查路径是否已包含有效的语言前缀
  if (!hasValidLocale(pathname)) {
    // 如果路径不包含有效语言前缀，添加默认语言
    const locale = getLocale(request);
    const redirectUrl = new URL(`/${locale}${pathname}`, request.url);
    return NextResponse.redirect(redirectUrl);
  }
  
  // 路径已包含有效语言前缀，继续处理
  return NextResponse.next();
}

/**
 * 中间件配置
 * 
 * 指定中间件应该在哪些路径上运行
 * 排除不需要国际化处理的路径
 */
export const config = {
  matcher: [
    // 匹配所有路径，除了：
    // - API 路由 (/api/...)
    // - Next.js 内部路径 (/_next/...)
    // - 静态文件（包含点号的路径）
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
