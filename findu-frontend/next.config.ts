/**
 * Next.js 配置文件
 *
 * 该文件用于配置 Next.js 应用的各种设置，包括：
 * - React 严格模式
 * - API 路由重写规则
 * - 开发工具支持
 *
 * 注意：移除了 i18n 配置以避免与 App Router 的 [locale] 动态路由冲突
 * 国际化功能通过 middleware.ts 和动态路由 [locale] 实现
 */

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 启用 React 严格模式，帮助发现潜在问题
  reactStrictMode: true,

  // Docker 部署配置：使用 standalone 输出模式
  output: process.env.NODE_ENV === 'production' && process.env.STATIC_EXPORT === 'true' ? 'export' : 'standalone',

  // 静态导出时的图片优化配置
  ...(process.env.NODE_ENV === 'production' && process.env.STATIC_EXPORT === 'true' ? {
    images: { unoptimized: true },
  } : {}),

  /**
   * API 路由重写配置
   * 用于将特定路径重写到内部 API 端点
   * 仅在非静态导出模式下启用
   */
  async rewrites() {
    if (process.env.NODE_ENV === 'production' && process.env.STATIC_EXPORT === 'true') {
      return [];
    }
    return [
      {
        // Chrome DevTools 调试支持
        source: '/.well-known/appspecific/com.chrome.devtools.json',
        destination: '/api/devtools',
      },
    ];
  },
  basePath: '/findu',
};

module.exports = nextConfig;