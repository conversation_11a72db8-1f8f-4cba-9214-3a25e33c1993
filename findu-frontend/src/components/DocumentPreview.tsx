/**
 * 文档预览组件
 *
 * 该组件用于预览和下载生成的需求文档。提供内嵌的文档预览功能
 * 和便捷的下载链接，让用户可以在生成文档后立即查看和获取文件。
 *
 * 功能特性：
 * - 内嵌 iframe 文档预览
 * - 文档下载功能
 * - 国际化支持
 * - 响应式设计
 * - 用户友好的界面
 */

import { useTranslation } from '@/lib/i18n';
import { Button } from '@/components/ui/Button';
import { downloadDemand } from '@/lib/api';
import { useState } from 'react';

/**
 * DocumentPreview 组件的属性接口
 */
interface DocumentPreviewProps {
  /** 文档数据对象，包含base64编码的文档数据和元信息 */
  documentData: {
    documentData: string;
    filename: string;
    mimeType: string;
    size: number;
    format: string;
  };
  /** 当前语言环境 */
  locale: string;
  /** 案例信息，用于重新生成不同格式的文档 */
  caseInfo?: {
    case_id: number;
    case_title: string;
    case_description: string;
    case_details: string[];
  };
}

/**
 * 文档预览组件
 *
 * 提供生成文档的预览和下载功能。使用 iframe 嵌入base64编码的文档内容，
 * 同时提供下载链接让用户可以保存文档到本地。支持PDF和Word格式的下载。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function DocumentPreview({ documentData, locale, caseInfo }: DocumentPreviewProps) {
  // 获取国际化翻译函数
  const { t } = useTranslation(locale);

  // 下载状态管理
  const [downloading, setDownloading] = useState<string | null>(null);

  // 创建blob URL用于预览
  const createBlobUrl = () => {
    try {
      const binaryString = atob(documentData.documentData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], { type: documentData.mimeType });
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('创建blob URL失败:', error);
      return null;
    }
  };

  // 下载文档
  const downloadDocument = async (format: 'pdf' | 'docx') => {
    if (!caseInfo) {
      console.error('缺少案例信息，无法下载文档');
      return;
    }

    try {
      setDownloading(format);

      // 如果请求的格式与当前格式相同，直接下载当前文档
      if (format === documentData.format) {
        const binaryString = atob(documentData.documentData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }

        const blob = new Blob([bytes], { type: documentData.mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = documentData.filename;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        // 如果需要不同格式，调用下载API
        await downloadDemand({
          case_id: caseInfo.case_id,
          case_title: caseInfo.case_title,
          case_description: caseInfo.case_description,
          case_details: caseInfo.case_details,
          locale,
          format,
        });
      }
    } catch (error) {
      console.error('下载文档失败:', error);
    } finally {
      setDownloading(null);
    }
  };

  const blobUrl = createBlobUrl();

  return (
    <div className="space-y-6">
      {/* 预览标题 */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          {t('document_preview')}
        </h3>
        <p className="text-gray-600">
          您的需求文档已生成完成，可以预览和下载
        </p>
        <p className="text-sm text-gray-500 mt-2">
          文件大小: {(documentData.size / 1024).toFixed(1)} KB
        </p>
      </div>

      {/* 文档预览 iframe */}
      {blobUrl && (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
          <iframe
            src={blobUrl}
            className="w-full h-[600px] border-0"
            title="Demand Document Preview"
          />
        </div>
      )}

      {/* 下载按钮组 */}
      <div className="flex justify-center space-x-4">
        <Button
          variant="primary"
          size="lg"
          className="px-8 py-3"
          onClick={() => downloadDocument('pdf')}
          disabled={downloading !== null}
          loading={downloading === 'pdf'}
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          下载 PDF
        </Button>

        <Button
          variant="secondary"
          size="lg"
          className="px-8 py-3"
          onClick={() => downloadDocument('docx')}
          disabled={downloading !== null}
          loading={downloading === 'docx'}
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          下载 Word
        </Button>
      </div>
    </div>
  );
}