/**
 * 主页面客户端组件
 * 
 * 该组件包含所有需要客户端交互的逻辑，包括状态管理、
 * 用户输入处理、API 调用等。从主页面组件中分离出来，
 * 以支持 Next.js 的服务器端渲染和静态生成。
 * 
 * 功能特性：
 * - 需求提示词输入和防抖处理
 * - 实时案例生成
 * - 案例选择和展示
 * - 需求文档生成和预览
 * - 错误处理和加载状态
 */

'use client';

import { useState } from 'react';
import PromptInput from '@/components/PromptInput';
import CaseCard from '@/components/CaseCard';
import DocumentPreview from '@/components/DocumentPreview';
import { generateCases, generateDemand } from '@/lib/api';
import { Case, DocumentData } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';
import { Button } from '@/components/ui/Button';
import { Toast, useToast } from '@/components/ui/Toast';

import { Card, CardContent } from '@/components/ui/Card';

/**
 * 主页面客户端组件属性接口
 */
interface HomeClientProps {
  /** 当前语言环境 */
  locale: string;
}

/**
 * 主页面客户端组件
 * 
 * 处理所有需要客户端交互的功能，包括用户输入、状态管理和 API 调用。
 * 
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function HomeClient({ locale }: HomeClientProps) {
  const { t } = useTranslation(locale);
  
  // 状态管理
  const [prompt, setPrompt] = useState<string>('');                    // 用户输入的提示词
  const [cases, setCases] = useState<Case[]>([]);                     // 生成的案例列表
  const [selectedCase, setSelectedCase] = useState<number | null>(null); // 用户选中的案例索引
  const [documentData, setDocumentData] = useState<DocumentData | null>(null); // 生成的文档数据
  const [loading, setLoading] = useState<boolean>(false);             // 加载状态
  const [error, setError] = useState<string | null>(null);            // 错误信息
  const { toast, showToast, hideToast } = useToast();                 // Toast 通知

  /**
   * 手动生成案例
   *
   * 当用户点击"生成案例"按钮时，调用 API 生成相关的需求案例。
   * 只有在用户主动触发时才会发送请求。
   */
  const handleGenerateCases = async () => {
    if (!prompt.trim()) {
      setError(t('error.empty_prompt'));
      return;
    }

    setLoading(true);
    setError(null);
    setCases([]); // 清空之前的案例
    setSelectedCase(null); // 重置选中状态
    setDocumentData(null); // 重置文档

    try {
      const data = await generateCases({ prompt: prompt.trim(), locale });
      setCases(data.cases);
      setLoading(false);
      showToast('案例生成成功！', 'success');
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '生成案例失败，请重试';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      setLoading(false);
    }
  };

  /**
   * 处理案例选择
   * 
   * 当用户点击选择某个案例时，更新选中状态并重置文档 URL。
   * 这确保了用户在选择新案例时不会看到之前案例的文档。
   * 
   * @param caseId - 被选中案例的索引
   */
  const handleSelectCase = (caseId: number) => {
    setSelectedCase(caseId);
    setDocumentData(null); // 重置需求单，确保不显示之前的文档
  };

  /**
   * 生成需求文档
   * 
   * 根据用户选择的案例，调用后端 API 生成详细的需求文档。
   * 包含完整的错误处理和加载状态管理。
   */
  const handleGenerateDocument = async () => {
    // 确保有选中的案例
    if (selectedCase === null) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await generateDemand(
        {
          case_id: selectedCase,
          case_title: cases[selectedCase].title,
          case_description: cases[selectedCase].description,
          case_details: cases[selectedCase].details,
          locale,
          format: 'pdf', // 默认生成PDF格式
        },
      );
      setDocumentData(data);
      setLoading(false);
      showToast('需求文档生成成功！', 'success');
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '生成需求文档失败，请重试';
      setError(errorMessage);
      showToast(errorMessage, 'error');
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* 动态背景效果 */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%239C92AC\' fill-opacity=\'0.1\'%3E%3Ccircle cx=\'30\' cy=\'30\' r=\'2\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        }}></div>
      </div>

      {/* 浮动的AI粒子效果 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-pulse opacity-40" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyan-400 rounded-full animate-float opacity-50" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-indigo-400 rounded-full animate-pulse opacity-30" style={{animationDelay: '3s'}}></div>
      </div>

      <div className="container mx-auto px-4 py-12 relative z-10">
        {/* 主区域 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 rounded-3xl mb-8 shadow-2xl shadow-blue-500/25 animate-float">
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <h1 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent mb-6 animate-pulse-slow">
            {t('title')}
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            {t('subtitle')}
          </p>
          <div className="flex justify-center">
            <div className="flex items-center space-x-8 text-sm text-gray-400">
              <div className="flex items-center bg-green-500/10 px-4 py-2 rounded-full border border-green-500/20">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-green-300 font-medium">AI驱动</span>
              </div>
              <div className="flex items-center bg-blue-500/10 px-4 py-2 rounded-full border border-blue-500/20">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse" style={{animationDelay: '0.5s'}}></div>
                <span className="text-blue-300 font-medium">智能分析</span>
              </div>
              <div className="flex items-center bg-purple-500/10 px-4 py-2 rounded-full border border-purple-500/20">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-2 animate-pulse" style={{animationDelay: '1s'}}></div>
                <span className="text-purple-300 font-medium">快速生成</span>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="max-w-4xl mx-auto">
          {/* 提示词输入卡片 */}
          <Card className="mb-12 shadow-2xl border border-cyan-500/20 bg-slate-800/90 backdrop-blur-xl">
            <CardContent className="p-10">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
                  <svg className="w-6 h-6 mr-2 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  描述您的项目需求
                </h2>
                <p className="text-gray-300 text-lg">
                  简单描述您想要开发的项目，AI将为您生成详细的案例方案
                </p>
              </div>

              <PromptInput
                prompt={prompt}
                onChange={(value) => setPrompt(value)}
                placeholder={t('prompt.placeholder')}
                disabled={loading}
              />

              {/* 生成案例按钮 */}
              <div className="flex justify-center mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={handleGenerateCases}
                  disabled={loading || !prompt.trim()}
                  loading={loading}
                  className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 shadow-2xl shadow-cyan-500/25 hover:shadow-cyan-500/40 transform hover:scale-105 transition-all duration-300 border-0"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  {t('generate_cases')}
                </Button>
              </div>
            </CardContent>
          </Card>


          {/* 加载状态 */}
          {loading && (
            <Card className="mb-12 bg-gradient-to-r from-slate-800/90 to-slate-700/90 border border-cyan-500/30 backdrop-blur-xl">
              <CardContent className="p-10">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full mb-6 shadow-2xl shadow-cyan-500/50 animate-pulse">
                    <div className="w-10 h-10 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <h3 className="text-2xl font-semibold text-white mb-2 flex items-center justify-center">
                    <svg className="w-6 h-6 mr-2 text-cyan-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    AI正在分析您的需求
                  </h3>
                  <p className="text-gray-300">
                    正在生成个性化的项目案例，请稍候...
                  </p>
                  <div className="mt-4 flex justify-center space-x-1">
                    <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 案例展示区域 */}
          {cases.length > 0 && (
            <div className="mb-12">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center">
                  <svg className="w-8 h-8 mr-3 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  为您推荐的项目案例
                </h2>
                <p className="text-gray-300 text-lg">
                  选择最符合您需求的案例，我们将为您生成详细的需求文档
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {cases.map((caseItem, idx) => (
                  <CaseCard
                    key={idx}
                    caseItem={caseItem}
                    isSelected={selectedCase === idx}
                    onSelect={() => handleSelectCase(idx)}
                    locale={locale}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 生成需求单按钮 */}
          {selectedCase !== null && (
            <div className="text-center mb-12">
              <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/90 to-slate-800/90 backdrop-blur-sm rounded-2xl p-8 border border-slate-600/50 shadow-2xl shadow-purple-900/20">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-2xl mb-4 shadow-lg">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-white mb-2">
                    准备生成需求文档
                  </h3>
                  <p className="text-slate-300">
                    基于您选择的案例，我们将生成详细的项目需求文档
                  </p>
                </div>
                <Button
                  variant="primary"
                  size="lg"
                  onClick={handleGenerateDocument}
                  disabled={loading}
                  loading={loading}
                  className="px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white rounded-xl border border-cyan-400/30 backdrop-blur-sm"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  {t('generate_document')}
                </Button>
              </div>
            </div>
          )}


          {/* 需求单预览和下载区域 */}
          {documentData && (
            <Card className="shadow-xl border-0 bg-gradient-to-br from-green-50 to-blue-50">
              <CardContent className="p-10">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-gray-800 mb-4">
                    🎉 需求文档生成完成！
                  </h3>
                  <p className="text-lg text-gray-600 mb-8">
                    您的项目需求文档已经准备就绪，可以预览和下载
                  </p>
                </div>
                <DocumentPreview
                  documentData={documentData}
                  locale={locale}
                  caseInfo={selectedCase !== null ? {
                    case_id: selectedCase,
                    case_title: cases[selectedCase].title,
                    case_description: cases[selectedCase].description,
                    case_details: cases[selectedCase].details,
                  } : undefined}
                />
              </CardContent>
            </Card>
          )}

          {/* 错误提示区域 */}
          {error && (
            <Card className="mb-8 border-red-200 bg-red-50">
              <CardContent className="p-6">
                <div className="flex items-center text-red-700">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Toast 通知组件 */}
      <Toast
        message={toast.message}
        type={toast.type}
        show={toast.show}
        onClose={hideToast}
      />
    </div>
  );
}
