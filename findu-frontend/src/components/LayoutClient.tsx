/**
 * 布局客户端组件
 *
 * 该组件处理需要客户端渲染的布局部分，包括导航栏和页脚。
 * 由于使用了 useTranslation hook，必须在客户端组件中运行。
 *
 * 功能特性：
 * - 现代化导航栏设计
 * - 语言切换功能
 * - 响应式布局
 * - 国际化支持
 */

'use client';

import Link from 'next/link';
import { useTranslation } from '@/lib/i18n';

/**
 * LayoutClient 组件的属性接口
 */
interface LayoutClientProps {
  /** 当前语言环境 */
  locale: string;
  /** 子组件内容 */
  children: React.ReactNode;
}

/**
 * 布局客户端组件
 *
 * 提供应用的主要布局结构，包括导航栏、主内容区域和页脚。
 * 支持国际化和现代化的UI设计。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function LayoutClient({ locale, children }: LayoutClientProps) {
  // 获取国际化翻译函数
  const { t } = useTranslation(locale);

  return (
    <>
      {/* 深色蓝紫色导航栏 */}
      <nav className="bg-gradient-to-r from-[#10162b] via-[#0e1a3d] to-[#10162b] shadow-2xl border-b border-[#0c1a34]/30 sticky top-0 z-50 backdrop-blur-sm">


        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            {/* 应用标题和Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <Link href={`/${locale}`} className="text-xl font-bold text-white hover:text-purple-300 transition-colors duration-300">
                {t('app_name')}
              </Link>
            </div>
            
            {/* 语言切换按钮 */}
            <div className="flex items-center space-x-2">
              <Link 
                href="/en" 
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  locale === 'en' 
                    ? 'bg-purple-600/80 text-white shadow-lg backdrop-blur-sm' 
                    : 'text-purple-200 hover:text-white hover:bg-purple-800/50 backdrop-blur-sm'
                }`}
              >
                {t('lang_en')}
              </Link>
              <Link 
                href="/zh" 
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  locale === 'zh' 
                    ? 'bg-purple-600/80 text-white shadow-lg backdrop-blur-sm' 
                    : 'text-purple-200 hover:text-white hover:bg-purple-800/50 backdrop-blur-sm'
                }`}
              >
                {t('lang_zh')}
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 主要内容区域 */}
      <main className="min-h-screen">
        {children}
      </main>

      {/* 现代化页脚 */}
      <footer className="bg-[#10162b] border-t border-[#0c1a34] mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-3 mb-4">
              <div className="w-6 h-6 bg-gradient-to-r from-[#10162b] to-[#0e1a3d] rounded-md flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-lg font-semibold text-white">{t('app_name')}</span>
            </div>
            <p className="text-gray-300 mb-2">{t('footer_text')}</p>
            <p className="text-sm text-gray-400">
              © 2024 {t('app_name')}. {t('all_rights_reserved')}
            </p>
          </div>
        </div>
      </footer>
    </>
  );
}
