import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import HomeClient from '../HomeClient';
import * as api from '@/lib/api';

// Mock the API module
jest.mock('@/lib/api');
const mockApi = api as jest.Mocked<typeof api>;

// Mock the translation hook
jest.mock('@/lib/i18n', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'generate_cases': 'Generate Cases',
        'generate_document': 'Generate Document',
        'error.empty_prompt': 'Please enter a prompt',
        'select_case': 'Select Case',
        'download_document': 'Download Document',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock child components
interface MockPromptInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled: boolean;
}

jest.mock('../PromptInput', () => {
  return function MockPromptInput({ value, onChange, onSubmit, disabled }: MockPromptInputProps) {
    return (
      <textarea
        data-testid="prompt-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && e.ctrlKey) {
            onSubmit();
          }
        }}
        disabled={disabled}
        placeholder="Enter your prompt"
      />
    );
  };
});

interface MockCaseCardProps {
  caseItem: { id: number; title: string; description: string };
  isSelected: boolean;
  onSelect: () => void;
}

jest.mock('../CaseCard', () => {
  return function MockCaseCard({ caseItem, isSelected, onSelect }: MockCaseCardProps) {
    return (
      <div
        data-testid={`case-card-${caseItem.id}`}
        onClick={onSelect}
        className={isSelected ? 'selected' : ''}
      >
        <h3>{caseItem.title}</h3>
        <p>{caseItem.description}</p>
      </div>
    );
  };
});

interface MockDocumentPreviewProps {
  documentData: { document_url: string };
}

jest.mock('../DocumentPreview', () => {
  return function MockDocumentPreview({ documentData }: MockDocumentPreviewProps) {
    return (
      <div data-testid="document-preview">
        <h3>Document Preview</h3>
        <p>Document URL: {documentData.document_url}</p>
      </div>
    );
  };
});

describe('HomeClient Component', () => {
  const mockCases = [
    {
      id: 0,
      title: 'E-commerce Website',
      description: 'Online shopping platform',
      details: ['User registration', 'Product catalog', 'Shopping cart'],
    },
    {
      id: 1,
      title: 'Blog Platform',
      description: 'Content management system',
      details: ['Article creation', 'User comments', 'SEO optimization'],
    },
  ];

  const mockDocumentData = {
    document_url: '/static/demands/test-document.pdf',
    content: 'Mock document content',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders initial state correctly', () => {
    render(<HomeClient locale="en" />);
    
    expect(screen.getByTestId('prompt-input')).toBeInTheDocument();
    expect(screen.getByText('Generate Cases')).toBeInTheDocument();
    expect(screen.getByText('Generate Cases')).toBeDisabled();
  });

  it('enables generate button when prompt is entered', async () => {
    const user = userEvent.setup();
    render(<HomeClient locale="en" />);
    
    const promptInput = screen.getByTestId('prompt-input');
    const generateButton = screen.getByText('Generate Cases');
    
    expect(generateButton).toBeDisabled();
    
    await user.type(promptInput, 'I want to build an e-commerce website');
    
    expect(generateButton).not.toBeDisabled();
  });

  it('generates cases successfully', async () => {
    const user = userEvent.setup();
    mockApi.generateCases.mockResolvedValue({ cases: mockCases });
    
    render(<HomeClient locale="en" />);
    
    const promptInput = screen.getByTestId('prompt-input');
    const generateButton = screen.getByText('Generate Cases');
    
    await user.type(promptInput, 'I want to build an e-commerce website');
    await user.click(generateButton);
    
    await waitFor(() => {
      expect(mockApi.generateCases).toHaveBeenCalledWith({
        prompt: 'I want to build an e-commerce website',
        locale: 'en',
      });
    });
    
    await waitFor(() => {
      expect(screen.getByTestId('case-card-0')).toBeInTheDocument();
      expect(screen.getByTestId('case-card-1')).toBeInTheDocument();
      expect(screen.getByText('E-commerce Website')).toBeInTheDocument();
      expect(screen.getByText('Blog Platform')).toBeInTheDocument();
    });
  });

  it('handles case generation error', async () => {
    const user = userEvent.setup();
    mockApi.generateCases.mockRejectedValue(new Error('API Error'));
    
    render(<HomeClient locale="en" />);
    
    const promptInput = screen.getByTestId('prompt-input');
    const generateButton = screen.getByText('Generate Cases');
    
    await user.type(promptInput, 'test prompt');
    await user.click(generateButton);
    
    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument();
    });
  });

  it('selects a case and enables document generation', async () => {
    const user = userEvent.setup();
    mockApi.generateCases.mockResolvedValue({ cases: mockCases });
    
    render(<HomeClient locale="en" />);
    
    // Generate cases first
    const promptInput = screen.getByTestId('prompt-input');
    await user.type(promptInput, 'test prompt');
    await user.click(screen.getByText('Generate Cases'));
    
    await waitFor(() => {
      expect(screen.getByTestId('case-card-0')).toBeInTheDocument();
    });
    
    // Select a case
    await user.click(screen.getByTestId('case-card-0'));
    
    await waitFor(() => {
      expect(screen.getByTestId('case-card-0')).toHaveClass('selected');
      expect(screen.getByText('Generate Document')).not.toBeDisabled();
    });
  });

  it('generates document successfully', async () => {
    const user = userEvent.setup();
    mockApi.generateCases.mockResolvedValue({ cases: mockCases });
    mockApi.generateDemand.mockResolvedValue(mockDocumentData);
    
    render(<HomeClient locale="en" />);
    
    // Generate cases and select one
    const promptInput = screen.getByTestId('prompt-input');
    await user.type(promptInput, 'test prompt');
    await user.click(screen.getByText('Generate Cases'));
    
    await waitFor(() => {
      expect(screen.getByTestId('case-card-0')).toBeInTheDocument();
    });
    
    await user.click(screen.getByTestId('case-card-0'));
    await user.click(screen.getByText('Generate Document'));
    
    await waitFor(() => {
      expect(mockApi.generateDemand).toHaveBeenCalledWith({
        case_id: 0,
        case_title: 'E-commerce Website',
        case_description: 'Online shopping platform',
        case_details: ['User registration', 'Product catalog', 'Shopping cart'],
        locale: 'en',
        format: 'pdf',
      });
    });
    
    await waitFor(() => {
      expect(screen.getByTestId('document-preview')).toBeInTheDocument();
      expect(screen.getByText('Document URL: /static/demands/test-document.pdf')).toBeInTheDocument();
    });
  });

  it('handles document generation error', async () => {
    const user = userEvent.setup();
    mockApi.generateCases.mockResolvedValue({ cases: mockCases });
    mockApi.generateDemand.mockRejectedValue(new Error('Document generation failed'));
    
    render(<HomeClient locale="en" />);
    
    // Generate cases and select one
    const promptInput = screen.getByTestId('prompt-input');
    await user.type(promptInput, 'test prompt');
    await user.click(screen.getByText('Generate Cases'));
    
    await waitFor(() => {
      expect(screen.getByTestId('case-card-0')).toBeInTheDocument();
    });
    
    await user.click(screen.getByTestId('case-card-0'));
    await user.click(screen.getByText('Generate Document'));
    
    await waitFor(() => {
      expect(screen.getByText('Document generation failed')).toBeInTheDocument();
    });
  });

  it('shows loading state during case generation', async () => {
    const user = userEvent.setup();
    // Create a promise that we can control
    let resolvePromise: (value: { cases: Array<{ id: number; title: string; description: string; details: string[] }> }) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });
    mockApi.generateCases.mockReturnValue(promise);
    
    render(<HomeClient locale="en" />);
    
    const promptInput = screen.getByTestId('prompt-input');
    await user.type(promptInput, 'test prompt');
    await user.click(screen.getByText('Generate Cases'));
    
    // Check loading state
    expect(screen.getByTestId('prompt-input')).toBeDisabled();
    
    // Resolve the promise
    resolvePromise!({ cases: mockCases });
    
    await waitFor(() => {
      expect(screen.getByTestId('prompt-input')).not.toBeDisabled();
    });
  });

  it('validates empty prompt', async () => {
    const user = userEvent.setup();
    render(<HomeClient locale="en" />);
    
    const generateButton = screen.getByText('Generate Cases');
    await user.click(generateButton);
    
    expect(screen.getByText('Please enter a prompt')).toBeInTheDocument();
    expect(mockApi.generateCases).not.toHaveBeenCalled();
  });
});
