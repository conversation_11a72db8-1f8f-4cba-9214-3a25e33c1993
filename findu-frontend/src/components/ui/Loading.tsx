/**
 * 加载状态组件库
 * 
 * 提供多种加载状态的视觉反馈，包括旋转器、骨架屏等。
 * 遵循现代化设计原则，提供良好的用户体验。
 * 
 * 功能特性：
 * - 多种加载动画（spinner, dots, pulse）
 * - 多种尺寸支持
 * - 骨架屏加载效果
 * - 自定义颜色支持
 * - 可配置的加载文本
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 加载组件属性接口
 */
interface LoadingProps {
  /** 加载动画类型 */
  type?: 'spinner' | 'dots' | 'pulse';
  /** 加载器尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 加载文本 */
  text?: string;
  /** 是否居中显示 */
  center?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 骨架屏组件属性接口
 */
interface SkeletonProps {
  /** 骨架屏类型 */
  variant?: 'text' | 'rectangular' | 'circular';
  /** 宽度 */
  width?: string | number;
  /** 高度 */
  height?: string | number;
  /** 自定义类名 */
  className?: string;
}

/**
 * 加载器尺寸样式映射
 */
const loadingSizes = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
};

/**
 * 旋转加载器组件
 */
const SpinnerLoader: React.FC<{ size: string }> = ({ size }) => (
  <svg
    className={cn('animate-spin', size)}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

/**
 * 点状加载器组件
 */
const DotsLoader: React.FC<{ size: string }> = ({ size }) => (
  <div className="flex space-x-1">
    {[0, 1, 2].map((i) => (
      <div
        key={i}
        className={cn(
          'bg-current rounded-full animate-pulse',
          size === 'w-4 h-4' ? 'w-1 h-1' : size === 'w-6 h-6' ? 'w-1.5 h-1.5' : 'w-2 h-2',
        )}
        style={{
          animationDelay: `${i * 0.2}s`,
          animationDuration: '1s',
        }}
      />
    ))}
  </div>
);

/**
 * 脉冲加载器组件
 */
const PulseLoader: React.FC<{ size: string }> = ({ size }) => (
  <div
    className={cn(
      'bg-current rounded-full animate-pulse',
      size,
    )}
  />
);

/**
 * 加载组件
 * 
 * 提供多种加载动画效果，可以根据不同场景选择合适的加载样式。
 * 支持自定义尺寸、文本和布局。
 * 
 * @param props - 加载组件属性
 * @returns React 函数组件
 */
export const Loading: React.FC<LoadingProps> = ({
  type = 'spinner',
  size = 'md',
  text,
  center = false,
  className,
}) => {
  const sizeClass = loadingSizes[size];

  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return <DotsLoader size={sizeClass} />;
      case 'pulse':
        return <PulseLoader size={sizeClass} />;
      default:
        return <SpinnerLoader size={sizeClass} />;
    }
  };

  return (
    <div
      className={cn(
        'flex items-center gap-2 text-gray-600',
        center && 'justify-center',
        className,
      )}
    >
      {renderLoader()}
      {text && <span className="text-sm">{text}</span>}
    </div>
  );
};

/**
 * 骨架屏组件
 * 
 * 提供内容加载时的占位效果，模拟实际内容的布局结构。
 * 
 * @param props - 骨架屏组件属性
 * @returns React 函数组件
 */
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width,
  height,
  className,
}) => {
  const baseClasses = 'animate-pulse bg-gray-200';
  
  const variantClasses = {
    text: 'h-4 rounded',
    rectangular: 'rounded',
    circular: 'rounded-full',
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        !width && variant === 'text' && 'w-full',
        !height && variant === 'rectangular' && 'h-20',
        !width && !height && variant === 'circular' && 'w-10 h-10',
        className,
      )}
      style={style}
    />
  );
};

export default Loading;
