/**
 * 文本域组件库
 * 
 * 提供统一的多行文本输入框样式和交互效果。
 * 遵循现代化设计原则，具有良好的可访问性和用户体验。
 * 
 * 功能特性：
 * - 自适应高度
 * - 多种尺寸支持
 * - 错误状态支持
 * - 字符计数功能
 * - 标签和帮助文本支持
 * - 完整的键盘导航支持
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';

/**
 * 文本域组件属性接口
 */
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** 文本域尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否有错误 */
  error?: boolean;
  /** 标签文本 */
  label?: string;
  /** 帮助文本 */
  helperText?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 是否自适应高度 */
  autoResize?: boolean;
  /** 是否显示字符计数 */
  showCount?: boolean;
  /** 最大字符数 */
  maxLength?: number;
}

/**
 * 文本域尺寸样式映射
 */
const textareaSizes = {
  sm: 'px-3 py-1.5 text-sm min-h-[80px]',
  md: 'px-4 py-2 text-base min-h-[100px]',
  lg: 'px-4 py-3 text-lg min-h-[120px]',
};

/**
 * 文本域组件
 * 
 * 提供一致的多行文本输入框样式和交互效果。支持自适应高度、
 * 字符计数等高级功能，具有良好的可访问性和用户体验。
 * 
 * @param props - 文本域属性
 * @returns React 函数组件
 */
export const Textarea: React.FC<TextareaProps> = ({
  size = 'md',
  error = false,
  label,
  helperText,
  errorMessage,
  autoResize = false,
  showCount = false,
  maxLength,
  className,
  id,
  value,
  onChange,
  ...props
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [textareaId, setTextareaId] = useState(id || '');

  // 在客户端生成ID以避免水合错误
  useEffect(() => {
    if (!id) {
      setTextareaId(`textarea-${Math.random().toString(36).substring(2, 11)}`);
    }
  }, [id]);

  /**
   * 自动调整文本域高度 - 只在客户端执行
   */
  const adjustHeight = useCallback(() => {
    if (autoResize && textareaRef.current && typeof window !== 'undefined') {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [autoResize]);

  // 当内容变化时调整高度
  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  /**
   * 处理输入变化
   */
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e);
    }
    adjustHeight();
  };

  // 计算当前字符数
  const currentLength = typeof value === 'string' ? value.length : 0;

  return (
    <div className="w-full">
      {/* 标签和字符计数 */}
      <div className="flex justify-between items-center mb-1">
        {label && (
          <label
            htmlFor={textareaId}
            className="block text-sm font-medium text-gray-700"
          >
            {label}
          </label>
        )}
        {showCount && (
          <span className="text-sm text-gray-500">
            {currentLength}{maxLength && `/${maxLength}`}
          </span>
        )}
      </div>

      {/* 文本域 */}
      <textarea
        ref={textareaRef}
        id={textareaId}
        value={value}
        onChange={handleChange}
        maxLength={maxLength}
        className={cn(
          // 基础样式
          'w-full rounded-lg border transition-all duration-200 resize-none',
          'focus:outline-none focus:ring-2 focus:ring-offset-1',
          'disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50',
          // 尺寸样式
          textareaSizes[size],
          // 状态样式
          error
            ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
            : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
          // 自定义样式
          className,
        )}
        {...props}
      />

      {/* 帮助文本或错误信息 */}
      {(helperText || errorMessage) && (
        <p
          className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500',
          )}
        >
          {error ? errorMessage : helperText}
        </p>
      )}
    </div>
  );
};

export default Textarea;
