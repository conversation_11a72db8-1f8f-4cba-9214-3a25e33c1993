/**
 * Toast 通知组件
 *
 * 该组件提供用户友好的通知功能，支持不同类型的消息显示。
 * 主要用于显示操作结果、错误信息和系统提示。
 *
 * 功能特性：
 * - 支持多种通知类型（成功、错误、警告、信息）
 * - 自动消失机制
 * - 优雅的动画效果
 * - 可手动关闭
 * - 响应式设计
 */

import React, { useEffect, useState, useCallback } from 'react';

/**
 * Toast 通知类型枚举
 */
export type ToastType = 'success' | 'error' | 'warning' | 'info';

/**
 * Toast 组件属性接口
 */
export interface ToastProps {
  /** 通知消息内容 */
  message: string;
  /** 通知类型，决定样式和图标 */
  type: ToastType;
  /** 是否显示通知 */
  show: boolean;
  /** 关闭回调函数 */
  onClose: () => void;
  /** 自动关闭延迟时间（毫秒），默认 5000ms */
  duration?: number;
}

/**
 * Toast 通知组件
 *
 * 在页面右上角显示通知消息，支持自动关闭和手动关闭。
 * 根据不同类型显示相应的颜色和图标。
 *
 * @param props - Toast 组件属性
 * @returns JSX 元素
 */
export const Toast: React.FC<ToastProps> = ({
  message,
  type,
  show,
  onClose,
  duration = 5000,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  /**
   * 处理关闭操作
   * 先隐藏组件，然后调用父组件的关闭回调
   */
  const handleClose = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300); // 等待动画完成
  }, [onClose]);

  /**
   * 处理显示状态变化
   * 当 show 为 true 时显示通知，并设置自动关闭定时器
   */
  useEffect(() => {
    if (show) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [show, duration, handleClose]);

  /**
   * 根据通知类型获取样式类名
   */
  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500 text-white';
      case 'error':
        return 'bg-red-500 text-white';
      case 'warning':
        return 'bg-yellow-500 text-white';
      case 'info':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  /**
   * 根据通知类型获取图标
   */
  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  // 如果不显示，返回 null
  if (!show) return null;

  return (
    <div className="fixed top-4 right-4 z-50">
      <div
        className={`
          flex items-center p-4 rounded-lg shadow-lg max-w-sm
          transform transition-all duration-300 ease-in-out
          ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
          ${getTypeStyles()}
        `}
      >
        {/* 图标 */}
        <div className="flex-shrink-0 mr-3">
          {getIcon()}
        </div>

        {/* 消息内容 */}
        <div className="flex-1 text-sm font-medium">
          {message}
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-3 text-white hover:text-gray-200 transition-colors duration-200"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  );
};

/**
 * Toast Hook
 * 提供便捷的 Toast 使用方式
 */
export const useToast = () => {
  const [toast, setToast] = useState<{
    show: boolean;
    message: string;
    type: ToastType;
  }>({
    show: false,
    message: '',
    type: 'info',
  });

  /**
   * 显示 Toast 通知
   */
  const showToast = (message: string, type: ToastType = 'info') => {
    setToast({
      show: true,
      message,
      type,
    });
  };

  /**
   * 隐藏 Toast 通知
   */
  const hideToast = () => {
    setToast(prev => ({
      ...prev,
      show: false,
    }));
  };

  return {
    toast,
    showToast,
    hideToast,
  };
};
