import { render, screen, fireEvent } from '@testing-library/react';
import { Card, CardHeader, CardContent, CardFooter } from '../Card';

describe('Card Component', () => {
  it('renders with default props', () => {
    render(
      <Card>
        <div>Card content</div>
      </Card>,
    );
    const card = screen.getByText('Card content').parentElement;
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('bg-white border border-gray-200'); // default variant
  });

  it('renders different variants correctly', () => {
    const { rerender } = render(<Card variant="default">Default</Card>);
    let card = screen.getByText('Default');
    expect(card).toHaveClass('bg-white border border-gray-200');

    rerender(<Card variant="elevated">Elevated</Card>);
    card = screen.getByText('Elevated');
    expect(card).toHaveClass('bg-white shadow-lg border border-gray-100');

    rerender(<Card variant="outlined">Outlined</Card>);
    card = screen.getByText('Outlined');
    expect(card).toHaveClass('bg-white border-2 border-gray-300');
  });

  it('handles clickable state correctly', () => {
    const handleClick = jest.fn();
    render(
      <Card clickable onClick={handleClick}>
        Clickable card
      </Card>,
    );
    
    const card = screen.getByText('Clickable card');
    expect(card).toHaveClass('cursor-pointer');
    
    fireEvent.click(card);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('applies hover effects when specified', () => {
    render(<Card hover>Hover card</Card>);
    const card = screen.getByText('Hover card');
    expect(card).toHaveClass('hover:shadow-lg hover:-translate-y-1');
  });

  it('applies hover effects for clickable cards', () => {
    render(<Card clickable>Clickable card</Card>);
    const card = screen.getByText('Clickable card');
    expect(card).toHaveClass('hover:shadow-lg hover:-translate-y-1');
  });

  it('applies custom className', () => {
    render(<Card className="custom-class">Custom card</Card>);
    const card = screen.getByText('Custom card');
    expect(card).toHaveClass('custom-class');
  });

  it('forwards other props correctly', () => {
    render(<Card data-testid="custom-card">Test card</Card>);
    const card = screen.getByTestId('custom-card');
    expect(card).toBeInTheDocument();
  });
});

describe('CardHeader Component', () => {
  it('renders with correct styling', () => {
    render(
      <CardHeader>
        <h2>Card Title</h2>
      </CardHeader>,
    );
    
    const header = screen.getByText('Card Title').parentElement;
    expect(header).toHaveClass('px-6 py-4 border-b border-gray-200');
  });

  it('applies custom className', () => {
    render(
      <CardHeader className="custom-header">
        Header content
      </CardHeader>,
    );
    
    const header = screen.getByText('Header content');
    expect(header).toHaveClass('custom-header');
  });
});

describe('CardContent Component', () => {
  it('renders with correct styling', () => {
    render(
      <CardContent>
        <p>Card content</p>
      </CardContent>,
    );
    
    const content = screen.getByText('Card content').parentElement;
    expect(content).toHaveClass('px-6 py-4');
  });

  it('applies custom className', () => {
    render(
      <CardContent className="custom-content">
        Content text
      </CardContent>,
    );
    
    const content = screen.getByText('Content text');
    expect(content).toHaveClass('custom-content');
  });
});

describe('CardFooter Component', () => {
  it('renders with correct styling', () => {
    render(
      <CardFooter>
        <button>Action</button>
      </CardFooter>,
    );
    
    const footer = screen.getByRole('button').parentElement;
    expect(footer).toHaveClass('px-6 py-4 border-t border-slate-600/50');
  });

  it('applies custom className', () => {
    render(
      <CardFooter className="custom-footer">
        Footer content
      </CardFooter>,
    );
    
    const footer = screen.getByText('Footer content');
    expect(footer).toHaveClass('custom-footer');
  });
});

describe('Card Composition', () => {
  it('renders complete card structure correctly', () => {
    render(
      <Card>
        <CardHeader>
          <h2>Title</h2>
        </CardHeader>
        <CardContent>
          <p>Content</p>
        </CardContent>
        <CardFooter>
          <button>Action</button>
        </CardFooter>
      </Card>,
    );
    
    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument();
  });
});
