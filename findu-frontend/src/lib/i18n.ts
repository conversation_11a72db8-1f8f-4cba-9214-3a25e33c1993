/**
 * 国际化 (i18n) 工具库
 *
 * 该文件提供应用程序的国际化功能，支持多语言文本翻译。
 * 目前支持英文 (en) 和中文 (zh) 两种语言，并提供了
 * 安全的翻译函数和回退机制。
 *
 * 功能特性：
 * - 多语言支持 (英文/中文)
 * - 嵌套键值翻译 (支持 'key.subkey' 格式)
 * - 自动回退到默认语言
 * - 类型安全的翻译函数
 */

import en from '@/locales/en.json';
import zh from '@/locales/zh.json';

/**
 * 翻译资源映射表
 * 包含所有支持语言的翻译文件
 */
const translations = {
  en,
  zh,
};

/**
 * 国际化 Hook 函数
 *
 * 提供翻译功能和当前语言信息。支持嵌套键值翻译，
 * 当指定语言不存在或翻译键不存在时，会自动回退到默认值。
 *
 * @param locale - 目标语言代码，如 'en' 或 'zh'
 * @returns 包含翻译函数和有效语言代码的对象
 */
export function useTranslation(locale: string | undefined) {
  // 默认回退语言
  const fallbackLocale = 'en';

  // 确定有效的语言代码，如果指定语言不存在则使用默认语言
  const effectiveLocale = locale && translations[locale as keyof typeof translations]
    ? locale
    : fallbackLocale;

  /**
   * 翻译函数
   *
   * 根据提供的键值获取对应的翻译文本。支持嵌套键值访问，
   * 例如 'error.api' 会访问 translations[locale].error.api
   *
   * @param key - 翻译键值，支持点号分隔的嵌套访问
   * @returns 翻译后的文本，如果找不到则返回原始键值
   */
  const t = (key: string): string => {
    // 将键值按点号分割为数组
    const keys = key.split('.');

    // 从当前语言的翻译对象开始查找
    let value: unknown = translations[effectiveLocale as keyof typeof translations];

    // 逐级访问嵌套属性
    for (const k of keys) {
      value = (value as Record<string, unknown>)?.[k] ?? key;
    }

    // 确保返回字符串类型，如果不是字符串则返回原始键值
    return typeof value === 'string' ? value : key;
  };

  return { t, locale: effectiveLocale };
}