/**
 * 应用程序类型定义文件
 *
 * 该文件包含整个应用程序中使用的 TypeScript 类型定义。
 * 提供了数据结构的类型安全保障，确保代码的可维护性和可读性。
 *
 * 功能特性：
 * - 核心数据结构类型定义
 * - 类型安全保障
 * - 代码智能提示支持
 * - 接口文档化
 */

/**
 * 需求案例数据结构接口
 *
 * 定义单个需求案例的完整数据格式，包含案例的基本信息
 * 和详细要求列表。这是应用程序中最核心的数据结构之一。
 */
export interface Case {
  /** 案例的唯一标识符，用于区分不同的案例 */
  id: number;

  /** 案例的标题，简洁描述案例的主要内容 */
  title: string;

  /** 案例的详细描述，提供案例的背景和概述信息 */
  description: string;

  /** 案例的详细要求列表，包含具体的功能需求和技术要求 */
  details: string[];

  /** 案例的主要功能列表（可选） */
  features?: string[];

  /** 案例使用的技术栈（可选） */
  technologies?: string[];

  /** 案例的复杂度等级，1-5级（可选） */
  complexity?: number;
}

/**
 * 文档数据接口
 * 定义生成的需求文档的数据结构
 */
export interface DocumentData {
  /** base64编码的文档数据 */
  documentData: string;
  /** 文件名 */
  filename: string;
  /** MIME类型 */
  mimeType: string;
  /** 文件大小（字节） */
  size: number;
  /** 文档格式 */
  format: string;
}