@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置和设置 */
@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #e2e8f0;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    color: #f8fafc;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

/* 组件样式 */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-slate-600 to-slate-700 border border-transparent rounded-lg shadow-sm hover:from-slate-700 hover:to-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 transform hover:scale-105;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-slate-200 bg-slate-800 border border-slate-600 rounded-lg shadow-sm hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200;
  }

  .card {
    @apply bg-slate-800 rounded-xl shadow-md border border-slate-700 overflow-hidden transition-all duration-200 hover:shadow-lg hover:border-slate-600;
  }

  .input-field {
    @apply w-full px-4 py-3 text-base border border-slate-600 bg-slate-800 text-slate-200 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200;
  }

  .textarea-field {
    @apply w-full px-4 py-3 text-base border border-slate-600 bg-slate-800 text-slate-200 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200 resize-none;
  }
}

/* 工具样式 */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #64748b, #94a3b8, #cbd5e1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-glass {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(100, 116, 139, 0.2);
  }

  .bg-glass-light {
    background: rgba(51, 65, 85, 0.6);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(148, 163, 184, 0.3);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(100, 116, 139, 0.15);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(148, 163, 184, 0.15);
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .bg-ai-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(100, 116, 139, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(148, 163, 184, 0.05) 0%, transparent 50%);
  }
}

/* 动画定义 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(100, 116, 139, 0.1), 0 0 10px rgba(100, 116, 139, 0.1), 0 0 15px rgba(100, 116, 139, 0.1);
  }
  100% {
    box-shadow: 0 0 10px rgba(100, 116, 139, 0.2), 0 0 20px rgba(100, 116, 139, 0.2), 0 0 30px rgba(100, 116, 139, 0.2);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}