<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FindU 前端样式测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 导入我们的自定义样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #e2e8f0;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.25;
            color: #f8fafc;
        }

        .btn-primary {
            @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-slate-600 to-slate-700 border border-transparent rounded-lg shadow-sm hover:from-slate-700 hover:to-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200 transform hover:scale-105;
        }

        .btn-secondary {
            @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium text-slate-200 bg-slate-800 border border-slate-600 rounded-lg shadow-sm hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-all duration-200;
        }

        .card {
            @apply bg-slate-800 rounded-xl shadow-md border border-slate-700 overflow-hidden transition-all duration-200 hover:shadow-lg hover:border-slate-600;
        }

        .input-field {
            @apply w-full px-4 py-3 text-base border border-slate-600 bg-slate-800 text-slate-200 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200;
        }

        .text-gradient {
            background: linear-gradient(135deg, #64748b, #94a3b8, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .bg-glass {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(100, 116, 139, 0.2);
        }

        .shadow-glow {
            box-shadow: 0 0 20px rgba(100, 116, 139, 0.15);
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- 标题区域 -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold mb-4">FindU 前端样式测试</h1>
                <p class="text-xl text-gradient">验证新的深色主题和柔和色调</p>
            </div>

            <!-- 按钮测试 -->
            <div class="card p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4">按钮样式测试</h2>
                <div class="flex flex-wrap gap-4">
                    <button class="btn-primary">主要按钮</button>
                    <button class="btn-secondary">次要按钮</button>
                    <button class="inline-flex items-center justify-center px-6 py-3 text-base font-medium border-2 border-slate-500 text-slate-300 hover:bg-slate-800 focus:ring-slate-500 bg-transparent rounded-lg transition-all duration-200">
                        轮廓按钮
                    </button>
                </div>
            </div>

            <!-- 表单测试 -->
            <div class="card p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4">表单元素测试</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">项目名称</label>
                        <input type="text" class="input-field" placeholder="请输入项目名称" value="FindU 项目">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">项目描述</label>
                        <textarea class="w-full px-4 py-3 text-base border border-slate-600 bg-slate-800 text-slate-200 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-all duration-200 resize-none" rows="4" placeholder="请输入项目描述">这是一个AI驱动的需求文档生成平台，采用了全新的深色主题设计。</textarea>
                    </div>
                </div>
            </div>

            <!-- 玻璃效果测试 -->
            <div class="bg-glass p-6 rounded-xl mb-8 shadow-glow">
                <h2 class="text-2xl font-semibold mb-4">玻璃效果卡片</h2>
                <p class="text-slate-300">这是一个带有玻璃效果的卡片，展示了新的背景模糊和边框样式。</p>
                <div class="mt-4">
                    <span class="text-gradient text-lg font-semibold">渐变文字效果</span>
                </div>
            </div>

            <!-- 功能展示 -->
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <div class="card p-6">
                    <h3 class="text-xl font-semibold mb-3">文档生成</h3>
                    <p class="text-slate-400 mb-4">支持PDF、Word、TXT多种格式的文档生成</p>
                    <div class="flex gap-2">
                        <span class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">PDF</span>
                        <span class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">DOCX</span>
                        <span class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">TXT</span>
                    </div>
                </div>
                
                <div class="card p-6">
                    <h3 class="text-xl font-semibold mb-3">AI 智能生成</h3>
                    <p class="text-slate-400 mb-4">基于AI的智能需求文档生成和优化</p>
                    <div class="flex gap-2">
                        <span class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">智能分析</span>
                        <span class="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">自动生成</span>
                    </div>
                </div>
            </div>

            <!-- 状态指示 -->
            <div class="card p-6">
                <h2 class="text-2xl font-semibold mb-4">系统状态</h2>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-slate-700 rounded-lg">
                        <div class="text-green-400 text-2xl font-bold">✓</div>
                        <div class="text-sm text-slate-300 mt-2">前端服务</div>
                        <div class="text-xs text-slate-400">运行正常</div>
                    </div>
                    <div class="text-center p-4 bg-slate-700 rounded-lg">
                        <div class="text-green-400 text-2xl font-bold">✓</div>
                        <div class="text-sm text-slate-300 mt-2">后端API</div>
                        <div class="text-xs text-slate-400">连接正常</div>
                    </div>
                    <div class="text-center p-4 bg-slate-700 rounded-lg">
                        <div class="text-yellow-400 text-2xl font-bold">⚠</div>
                        <div class="text-sm text-slate-300 mt-2">AI服务</div>
                        <div class="text-xs text-slate-400">配置待完善</div>
                    </div>
                </div>
            </div>

            <!-- 测试结果 -->
            <div class="mt-8 text-center">
                <h2 class="text-2xl font-semibold mb-4">样式测试结果</h2>
                <div class="bg-green-900/20 border border-green-700 rounded-lg p-4">
                    <div class="text-green-400 text-lg font-semibold">✅ 样式更新成功</div>
                    <p class="text-slate-300 mt-2">
                        已成功将亮色主题调整为深色主题，使用了更柔和的slate色系，
                        提升了整体的视觉一致性和专业感。
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
