#!/bin/bash

# FindU Test Runner Script
# This script runs all tests for both frontend and backend

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd findu-frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm ci
    fi
    
    # Run linting
    print_status "Running ESLint..."
    npm run lint
    
    # Run TypeScript check
    print_status "Running TypeScript check..."
    npx tsc --noEmit
    
    # Run unit tests
    print_status "Running frontend unit tests..."
    npm run test:ci
    
    # Run E2E tests if requested
    if [ "$RUN_E2E" = "true" ]; then
        print_status "Running E2E tests..."
        npm run e2e
    fi
    
    cd ..
    print_success "Frontend tests completed!"
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."
    
    cd findu-backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    pip install -r requirements.txt
    
    # Run code formatting check
    print_status "Running Black formatter check..."
    black --check --diff .
    
    # Run import sorting check
    print_status "Running isort check..."
    isort --check-only --diff .
    
    # Run type checking
    print_status "Running mypy type checking..."
    mypy app --ignore-missing-imports
    
    # Run tests
    print_status "Running backend tests..."
    pytest
    
    # Run performance tests if requested
    if [ "$RUN_PERFORMANCE" = "true" ]; then
        print_status "Running performance tests..."
        pytest -m performance
    fi
    
    deactivate
    cd ..
    print_success "Backend tests completed!"
}

# Function to run security checks
run_security_checks() {
    print_status "Running security checks..."
    
    # Frontend security check
    if command_exists npm; then
        cd findu-frontend
        print_status "Running npm audit..."
        npm audit --audit-level moderate || print_warning "npm audit found issues"
        cd ..
    fi
    
    # Backend security check
    if command_exists bandit; then
        cd findu-backend
        print_status "Running bandit security check..."
        bandit -r app/ || print_warning "bandit found security issues"
        cd ..
    fi
    
    print_success "Security checks completed!"
}

# Function to generate coverage report
generate_coverage_report() {
    print_status "Generating coverage reports..."
    
    # Frontend coverage
    if [ -f "findu-frontend/coverage/lcov.info" ]; then
        print_status "Frontend coverage report available at: findu-frontend/coverage/lcov-report/index.html"
    fi
    
    # Backend coverage
    if [ -f "findu-backend/htmlcov/index.html" ]; then
        print_status "Backend coverage report available at: findu-backend/htmlcov/index.html"
    fi
    
    print_success "Coverage reports generated!"
}

# Function to start services for testing
start_test_services() {
    print_status "Starting test services..."
    
    # Start MongoDB for testing
    if command_exists docker; then
        print_status "Starting MongoDB container..."
        docker run -d --name findu-test-mongo -p 27017:27017 mongo:5.0 || print_warning "MongoDB container already running"
        sleep 5  # Wait for MongoDB to start
    else
        print_warning "Docker not found. Please ensure MongoDB is running on localhost:27017"
    fi
}

# Function to stop test services
stop_test_services() {
    print_status "Stopping test services..."
    
    if command_exists docker; then
        print_status "Stopping MongoDB container..."
        docker stop findu-test-mongo >/dev/null 2>&1 || true
        docker rm findu-test-mongo >/dev/null 2>&1 || true
    fi
}

# Function to run all tests
run_all_tests() {
    print_status "Starting FindU test suite..."
    
    # Start test services
    start_test_services
    
    # Set environment variables for testing
    export ENVIRONMENT=test
    export MONGODB_URL=mongodb://localhost:27017/test_db
    export X=test_api_key
    export JWT_SECRET=test_jwt_secret
    export NEXT_PUBLIC_API_URL=http://localhost:8000
    
    # Run tests
    if [ "$SKIP_FRONTEND" != "true" ]; then
        run_frontend_tests
    fi
    
    if [ "$SKIP_BACKEND" != "true" ]; then
        run_backend_tests
    fi
    
    if [ "$RUN_SECURITY" = "true" ]; then
        run_security_checks
    fi
    
    # Generate coverage reports
    generate_coverage_report
    
    # Stop test services
    stop_test_services
    
    print_success "All tests completed successfully! 🎉"
}

# Function to show help
show_help() {
    echo "FindU Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --frontend-only     Run only frontend tests"
    echo "  --backend-only      Run only backend tests"
    echo "  --with-e2e          Include E2E tests"
    echo "  --with-performance  Include performance tests"
    echo "  --with-security     Include security checks"
    echo "  --skip-frontend     Skip frontend tests"
    echo "  --skip-backend      Skip backend tests"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                          # Run all tests"
    echo "  $0 --frontend-only          # Run only frontend tests"
    echo "  $0 --backend-only           # Run only backend tests"
    echo "  $0 --with-e2e               # Run all tests including E2E"
    echo "  $0 --with-performance       # Run all tests including performance"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --frontend-only)
            SKIP_BACKEND=true
            shift
            ;;
        --backend-only)
            SKIP_FRONTEND=true
            shift
            ;;
        --with-e2e)
            RUN_E2E=true
            shift
            ;;
        --with-performance)
            RUN_PERFORMANCE=true
            shift
            ;;
        --with-security)
            RUN_SECURITY=true
            shift
            ;;
        --skip-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        --skip-backend)
            SKIP_BACKEND=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check prerequisites
print_status "Checking prerequisites..."

if [ "$SKIP_FRONTEND" != "true" ] && ! command_exists node; then
    print_error "Node.js is required but not installed."
    exit 1
fi

if [ "$SKIP_BACKEND" != "true" ] && ! command_exists python; then
    print_error "Python is required but not installed."
    exit 1
fi

# Run tests
run_all_tests
