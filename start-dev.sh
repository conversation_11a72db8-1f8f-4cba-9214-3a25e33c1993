#!/bin/bash

# FindU 开发环境启动脚本

echo "🚀 启动 FindU 开发环境..."

# 检查是否安装了必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装，请先安装 Python 3.8+"
        exit 1
    fi
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        echo "✅ Docker 已安装"
    else
        echo "⚠️  Docker 未安装，将使用本地开发模式"
    fi
    
    echo "✅ 系统要求检查完成"
}

# 安装前端依赖
install_frontend() {
    echo "📦 安装前端依赖..."
    cd findu-frontend
    if [ ! -d "node_modules" ]; then
        npm install
    else
        echo "✅ 前端依赖已安装"
    fi
    cd ..
}

# 安装后端依赖
install_backend() {
    echo "📦 安装后端依赖..."
    cd findu-backend
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # 激活虚拟环境并安装依赖
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
}

# 设置环境变量
setup_env() {
    echo "⚙️  设置环境变量..."
    
    # 前端环境变量
    if [ ! -f "findu-frontend/.env.local" ]; then
        cp findu-frontend/.env.example findu-frontend/.env.local
        echo "✅ 已创建前端环境变量文件"
    fi
    
    # 后端环境变量
    if [ ! -f "findu-backend/.env" ]; then
        cp findu-backend/.env.example findu-backend/.env
        echo "✅ 已创建后端环境变量文件"
        echo "⚠️  请编辑 findu-backend/.env 文件，配置您的 AI_API_KEY"
    fi
}

# 启动服务
start_services() {
    echo "🚀 启动服务..."
    
    # 启动后端
    echo "启动后端服务..."
    cd findu-backend
    source venv/bin/activate
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 启动前端
    echo "启动前端服务..."
    cd findu-frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo "✅ 服务启动完成！"
    echo "🌐 前端地址: http://localhost:3000"
    echo "🔧 后端地址: http://localhost:8000"
    echo "📚 API文档: http://localhost:8000/docs"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap "echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
}

# 主函数
main() {
    check_requirements
    install_frontend
    install_backend
    setup_env
    start_services
}

# 运行主函数
main
