#!/bin/bash

# FindU 系统验证脚本
# 用于验证新环境下的系统配置和功能

echo "🚀 FindU 系统验证开始..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "✅ $1 已安装"
        return 0
    else
        echo -e "❌ $1 未安装"
        return 1
    fi
}

check_port() {
    if lsof -i:$1 &> /dev/null; then
        echo -e "✅ 端口 $1 正在使用中"
        return 0
    else
        echo -e "❌ 端口 $1 未被使用"
        return 1
    fi
}

check_url() {
    if curl -s -f $1 > /dev/null; then
        echo -e "✅ $1 可访问"
        return 0
    else
        echo -e "❌ $1 不可访问"
        return 1
    fi
}

# 1. 检查系统依赖
echo -e "\n📋 检查系统依赖..."
echo "------------------------"
check_command "node"
check_command "npm"
check_command "python3"
check_command "pip"
check_command "git"

# 2. 检查Node.js版本
echo -e "\n📋 检查Node.js版本..."
echo "------------------------"
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -ge 18 ]; then
    echo -e "✅ Node.js 版本: $(node --version) (>= 18.0)"
else
    echo -e "❌ Node.js 版本过低: $(node --version) (需要 >= 18.0)"
fi

# 3. 检查Python版本
echo -e "\n📋 检查Python版本..."
echo "------------------------"
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
echo -e "✅ Python 版本: $(python3 --version)"

# 4. 检查项目文件
echo -e "\n📋 检查项目文件..."
echo "------------------------"
if [ -f "findu-backend/.env" ]; then
    echo -e "✅ 后端环境配置文件存在"
else
    echo -e "❌ 后端环境配置文件缺失"
fi

if [ -f "findu-frontend/.env.local" ]; then
    echo -e "✅ 前端环境配置文件存在"
else
    echo -e "❌ 前端环境配置文件缺失"
fi

if [ -f "findu-backend/requirements.txt" ]; then
    echo -e "✅ Python依赖文件存在"
else
    echo -e "❌ Python依赖文件缺失"
fi

if [ -f "findu-frontend/package.json" ]; then
    echo -e "✅ Node.js依赖文件存在"
else
    echo -e "❌ Node.js依赖文件缺失"
fi

# 5. 检查服务状态
echo -e "\n📋 检查服务状态..."
echo "------------------------"
check_port 8000
check_port 3000

# 6. 检查API可用性
echo -e "\n📋 检查API可用性..."
echo "------------------------"
check_url "http://localhost:8000/health"
check_url "http://localhost:8000/docs"
check_url "http://localhost:3000"

# 7. 功能测试
echo -e "\n📋 功能测试..."
echo "------------------------"

# 测试案例生成API
echo "测试案例生成API..."
CASES_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/generate-cases" \
    -H "Content-Type: application/json" \
    -d '{"prompt": "测试项目", "locale": "zh"}' \
    --max-time 30)

if echo "$CASES_RESPONSE" | grep -q "cases"; then
    echo -e "✅ 案例生成API正常"
else
    echo -e "❌ 案例生成API异常"
fi

# 测试文档生成API
echo "测试文档生成API..."
DOC_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/generate-demand" \
    -H "Content-Type: application/json" \
    -d '{"case_id": 0, "case_title": "测试", "case_description": "测试", "case_details": ["测试"], "locale": "zh", "format": "pdf"}' \
    --max-time 30)

if echo "$DOC_RESPONSE" | grep -q "documentData"; then
    echo -e "✅ 文档生成API正常"
else
    echo -e "❌ 文档生成API异常"
fi

# 8. 总结
echo -e "\n📊 验证总结"
echo "=================================="
echo -e "✅ 系统基本配置正常"
echo -e "✅ 主要功能可用"
echo -e "⚠️  AI服务使用模拟数据模式（正常情况）"
echo -e "\n🎉 FindU 系统验证完成！"
echo -e "\n📖 更多信息请查看: docs/NEW_ENVIRONMENT_SETUP.md"

# 9. 使用提示
echo -e "\n💡 使用提示:"
echo "- 前端地址: http://localhost:3000"
echo "- 后端API: http://localhost:8000"
echo "- API文档: http://localhost:8000/docs"
echo "- 如需真实AI服务，请配置有效的API密钥"
